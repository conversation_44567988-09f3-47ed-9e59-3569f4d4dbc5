# Environment Configuration Example
# Copy this file to .env and modify the values as needed

# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=AI Watermark Removal Service
VERSION=1.0.0
DESCRIPTION=Advanced AI-powered watermark removal service

# Server Settings
HOST=0.0.0.0
PORT=8000
DEBUG=false

# Model Settings
MODEL_PATH=models/
DEVICE=cuda
MAX_IMAGE_SIZE=2048
BATCH_SIZE=1
MAX_CONCURRENT_REQUESTS=10
PROCESSING_TIMEOUT=300

# Storage Settings
UPLOAD_DIR=uploads/
OUTPUT_DIR=outputs/
TEMP_DIR=temp/
MAX_FILE_SIZE=52428800  # 50MB in bytes

# Redis Settings (for task queue)
REDIS_URL=redis://localhost:6379

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Security (for production)
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Database (if needed for future features)
DATABASE_URL=sqlite:///./watermark_removal.db

# Monitoring (optional)
ENABLE_METRICS=true
METRICS_PORT=9090

# Performance Tuning
TORCH_NUM_THREADS=4
OMP_NUM_THREADS=4
CUDA_VISIBLE_DEVICES=0

# Model Optimization
ENABLE_MODEL_QUANTIZATION=false
ENABLE_TENSORRT=false
ENABLE_ONNX_OPTIMIZATION=false
