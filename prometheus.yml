# Prometheus configuration for monitoring the watermark removal service

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Watermark removal service metrics (if implemented)
  - job_name: 'watermark-service'
    static_configs:
      - targets: ['watermark-removal:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Redis metrics (if redis exporter is used)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Nginx metrics (if nginx exporter is used)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s

  # GPU metrics (if nvidia-smi exporter is used)
  - job_name: 'gpu'
    static_configs:
      - targets: ['gpu-exporter:9445']
    scrape_interval: 30s

# Alerting rules (example)
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           - alertmanager:9093
