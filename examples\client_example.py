#!/usr/bin/env python3
"""
Example client for the AI Watermark Removal Service.
"""
import requests
import base64
import json
import time
from pathlib import Path
from PIL import Image
import io


class WatermarkRemovalClient:
    """Client for interacting with the watermark removal API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.api_url = f"{self.base_url}/api/v1"
        
    def health_check(self):
        """Check if the service is healthy."""
        try:
            response = requests.get(f"{self.base_url}/health")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Health check failed: {e}")
            return None
    
    def get_service_info(self):
        """Get service information."""
        try:
            response = requests.get(f"{self.base_url}/info")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Failed to get service info: {e}")
            return None
    
    def get_available_models(self):
        """Get available AI models."""
        try:
            response = requests.get(f"{self.api_url}/watermark/models")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Failed to get models: {e}")
            return None
    
    def remove_watermark(self, image_path: str, model_type: str = "unet", 
                        enhance_quality: bool = True, output_format: str = "png"):
        """Remove watermark from an image."""
        try:
            with open(image_path, 'rb') as f:
                files = {'image': (Path(image_path).name, f, 'image/jpeg')}
                data = {
                    'model_type': model_type,
                    'enhance_quality': enhance_quality,
                    'return_format': output_format
                }
                
                response = requests.post(
                    f"{self.api_url}/watermark/remove",
                    files=files,
                    data=data
                )
                response.raise_for_status()
                return response.json()
                
        except requests.RequestException as e:
            print(f"Failed to remove watermark: {e}")
            return None
        except FileNotFoundError:
            print(f"Image file not found: {image_path}")
            return None
    
    def remove_watermark_stream(self, image_path: str, output_path: str,
                               model_type: str = "unet", enhance_quality: bool = True):
        """Remove watermark and save result directly."""
        try:
            with open(image_path, 'rb') as f:
                files = {'image': (Path(image_path).name, f, 'image/jpeg')}
                data = {
                    'model_type': model_type,
                    'enhance_quality': enhance_quality,
                    'return_format': 'png'
                }
                
                response = requests.post(
                    f"{self.api_url}/watermark/remove-stream",
                    files=files,
                    data=data
                )
                response.raise_for_status()
                
                # Save the result
                with open(output_path, 'wb') as out_f:
                    out_f.write(response.content)
                
                return True
                
        except requests.RequestException as e:
            print(f"Failed to remove watermark: {e}")
            return False
        except FileNotFoundError:
            print(f"Image file not found: {image_path}")
            return False
    
    def analyze_watermark(self, image_path: str):
        """Analyze image for watermark characteristics."""
        try:
            with open(image_path, 'rb') as f:
                files = {'image': (Path(image_path).name, f, 'image/jpeg')}
                
                response = requests.post(
                    f"{self.api_url}/watermark/analyze",
                    files=files
                )
                response.raise_for_status()
                return response.json()
                
        except requests.RequestException as e:
            print(f"Failed to analyze watermark: {e}")
            return None
        except FileNotFoundError:
            print(f"Image file not found: {image_path}")
            return None
    
    def compare_models(self, image_path: str, models: list = None):
        """Compare different models on the same image."""
        if models is None:
            models = ["unet", "diffusion"]
        
        try:
            with open(image_path, 'rb') as f:
                files = {'image': (Path(image_path).name, f, 'image/jpeg')}
                data = {'models': models}
                
                response = requests.post(
                    f"{self.api_url}/watermark/compare",
                    files=files,
                    data=data
                )
                response.raise_for_status()
                return response.json()
                
        except requests.RequestException as e:
            print(f"Failed to compare models: {e}")
            return None
        except FileNotFoundError:
            print(f"Image file not found: {image_path}")
            return None
    
    def create_batch_job(self, image_paths: list, model_type: str = "unet",
                        enhance_quality: bool = True, output_format: str = "png"):
        """Create a batch processing job."""
        try:
            files = []
            for image_path in image_paths:
                with open(image_path, 'rb') as f:
                    files.append(('images', (Path(image_path).name, f.read(), 'image/jpeg')))
            
            data = {
                'model_type': model_type,
                'enhance_quality': enhance_quality,
                'output_format': output_format
            }
            
            response = requests.post(
                f"{self.api_url}/batch/process",
                files=files,
                data=data
            )
            response.raise_for_status()
            return response.json()
            
        except requests.RequestException as e:
            print(f"Failed to create batch job: {e}")
            return None
        except FileNotFoundError as e:
            print(f"Image file not found: {e}")
            return None
    
    def get_batch_status(self, job_id: str):
        """Get batch job status."""
        try:
            response = requests.get(f"{self.api_url}/batch/status/{job_id}")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Failed to get batch status: {e}")
            return None
    
    def wait_for_batch_completion(self, job_id: str, timeout: int = 300):
        """Wait for batch job to complete."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_batch_status(job_id)
            if not status:
                return None
            
            print(f"Job {job_id}: {status['status']} - {status['progress_percentage']:.1f}%")
            
            if status['status'] in ['completed', 'failed', 'cancelled']:
                return status
            
            time.sleep(5)  # Wait 5 seconds before checking again
        
        print(f"Timeout waiting for job {job_id}")
        return None
    
    def download_batch_results(self, job_id: str, output_path: str):
        """Download batch processing results."""
        try:
            response = requests.get(f"{self.api_url}/batch/download/{job_id}")
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            return True
            
        except requests.RequestException as e:
            print(f"Failed to download batch results: {e}")
            return False


def save_base64_image(base64_data: str, output_path: str):
    """Save base64 encoded image to file."""
    try:
        image_data = base64.b64decode(base64_data)
        image = Image.open(io.BytesIO(image_data))
        image.save(output_path)
        return True
    except Exception as e:
        print(f"Failed to save image: {e}")
        return False


def main():
    """Example usage of the watermark removal client."""
    # Initialize client
    client = WatermarkRemovalClient("http://localhost:8000")
    
    # Check service health
    print("🔍 Checking service health...")
    health = client.health_check()
    if not health:
        print("❌ Service is not available")
        return
    
    print(f"✅ Service is {health['status']}")
    
    # Get service info
    print("\n📋 Getting service information...")
    info = client.get_service_info()
    if info:
        print(f"Service: {info['service']}")
        print(f"Version: {info['version']}")
        print(f"Supported formats: {info['supported_formats']}")
    
    # Get available models
    print("\n🤖 Getting available models...")
    models = client.get_available_models()
    if models:
        print(f"Available models: {models['available_models']}")
    
    # Example image path (you need to provide an actual image)
    image_path = "example_watermarked_image.jpg"
    
    if not Path(image_path).exists():
        print(f"\n⚠️  Example image not found: {image_path}")
        print("Please provide a watermarked image to test with.")
        return
    
    # Analyze watermark
    print(f"\n🔍 Analyzing watermark in {image_path}...")
    analysis = client.analyze_watermark(image_path)
    if analysis:
        print(f"Watermark detected: {analysis['watermark_detected']}")
        print(f"Confidence: {analysis['confidence']:.2f}")
        print(f"Recommended model: {analysis['recommended_model']}")
    
    # Remove watermark
    print(f"\n🎯 Removing watermark from {image_path}...")
    result = client.remove_watermark(image_path, model_type="unet")
    if result and result['status'] == 'completed':
        print(f"✅ Watermark removed successfully!")
        print(f"Processing time: {result['processing_time']:.2f}s")
        print(f"Confidence: {result['confidence']:.2f}")
        
        # Save result
        output_path = "result_no_watermark.png"
        if save_base64_image(result['processed_image'], output_path):
            print(f"💾 Result saved to {output_path}")
    
    # Stream example
    print(f"\n🌊 Using stream API...")
    stream_output = "result_stream.png"
    if client.remove_watermark_stream(image_path, stream_output):
        print(f"✅ Stream result saved to {stream_output}")
    
    # Compare models
    print(f"\n⚖️  Comparing models...")
    comparison = client.compare_models(image_path, ["unet", "diffusion"])
    if comparison:
        print(f"Comparison ID: {comparison['comparison_id']}")
        for model, result in comparison['results'].items():
            print(f"  {model}: {result['processing_time']:.2f}s, confidence: {result['confidence']:.2f}")
    
    print("\n🎉 Example completed!")


if __name__ == "__main__":
    main()
