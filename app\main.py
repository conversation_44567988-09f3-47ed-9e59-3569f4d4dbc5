"""
Main FastAPI application for AI watermark removal service.
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
from contextlib import asynccontextmanager

from app.core.config import settings
from app.api.v1 import api_router
from app.services.model_manager import ModelManager


# Global model manager instance
model_manager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan events."""
    global model_manager
    
    # Startup
    print("🚀 Starting AI Watermark Removal Service...")
    model_manager = ModelManager(device=settings.DEVICE)
    await model_manager.initialize()
    print(f"✅ Models loaded successfully on {settings.DEVICE}")
    
    yield
    
    # Shutdown
    print("🛑 Shutting down service...")
    if model_manager:
        await model_manager.cleanup()
    print("✅ Cleanup completed")


# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# Include API routes
app.include_router(api_router, prefix=settings.API_V1_STR)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")


@app.get("/")
async def root():
    """Root endpoint with service information."""
    return {
        "service": settings.PROJECT_NAME,
        "version": settings.VERSION,
        "description": settings.DESCRIPTION,
        "status": "running",
        "docs": "/docs",
        "api": settings.API_V1_STR
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    global model_manager
    
    status = "healthy"
    details = {
        "service": "running",
        "models": "not_loaded"
    }
    
    if model_manager and model_manager.is_ready():
        details["models"] = "loaded"
    else:
        status = "degraded"
    
    return {
        "status": status,
        "details": details
    }


@app.get("/info")
async def service_info():
    """Get detailed service information."""
    global model_manager
    
    info = {
        "service": settings.PROJECT_NAME,
        "version": settings.VERSION,
        "device": settings.DEVICE,
        "max_image_size": settings.MAX_IMAGE_SIZE,
        "supported_formats": settings.SUPPORTED_FORMATS,
        "max_file_size_mb": settings.MAX_FILE_SIZE // (1024 * 1024),
        "models": {}
    }
    
    if model_manager:
        info["models"] = model_manager.get_model_info()
    
    return info


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
