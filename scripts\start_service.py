#!/usr/bin/env python3
"""
Service startup script with health checks and monitoring.
"""
import subprocess
import sys
import time
import requests
import argparse
from pathlib import Path
import signal
import os


class ServiceManager:
    """Manages the watermark removal service lifecycle."""
    
    def __init__(self, host="0.0.0.0", port=8000, workers=1, reload=False):
        self.host = host
        self.port = port
        self.workers = workers
        self.reload = reload
        self.process = None
        self.base_url = f"http://{host}:{port}"
        
    def start_service(self):
        """Start the service using uvicorn."""
        print(f"🚀 Starting AI Watermark Removal Service...")
        print(f"Host: {self.host}")
        print(f"Port: {self.port}")
        print(f"Workers: {self.workers}")
        print(f"Reload: {self.reload}")
        
        # Build command
        if self.workers > 1:
            # Use gunicorn for multiple workers
            cmd = [
                "gunicorn",
                "app.main:app",
                "-w", str(self.workers),
                "-k", "uvicorn.workers.UvicornWorker",
                "--bind", f"{self.host}:{self.port}",
                "--timeout", "300",
                "--keep-alive", "2",
                "--max-requests", "1000",
                "--max-requests-jitter", "100"
            ]
        else:
            # Use uvicorn for single worker
            cmd = [
                "uvicorn",
                "app.main:app",
                "--host", self.host,
                "--port", str(self.port)
            ]
            
            if self.reload:
                cmd.append("--reload")
        
        try:
            self.process = subprocess.Popen(cmd)
            print(f"✅ Service started with PID: {self.process.pid}")
            
            # Wait for service to be ready
            if self.wait_for_service():
                print("🎉 Service is ready!")
                return True
            else:
                print("❌ Service failed to start properly")
                self.stop_service()
                return False
                
        except Exception as e:
            print(f"❌ Failed to start service: {e}")
            return False
    
    def wait_for_service(self, timeout=60):
        """Wait for service to be ready."""
        print("⏳ Waiting for service to be ready...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.base_url}/health", timeout=5)
                if response.status_code == 200:
                    return True
            except requests.RequestException:
                pass
            
            time.sleep(2)
            print(".", end="", flush=True)
        
        print()
        return False
    
    def stop_service(self):
        """Stop the service."""
        if self.process:
            print("🛑 Stopping service...")
            self.process.terminate()
            
            # Wait for graceful shutdown
            try:
                self.process.wait(timeout=10)
                print("✅ Service stopped gracefully")
            except subprocess.TimeoutExpired:
                print("⚠️  Force killing service...")
                self.process.kill()
                self.process.wait()
                print("✅ Service force stopped")
            
            self.process = None
    
    def restart_service(self):
        """Restart the service."""
        print("🔄 Restarting service...")
        self.stop_service()
        time.sleep(2)
        return self.start_service()
    
    def get_service_status(self):
        """Get service status."""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.json()
        except requests.RequestException:
            return {"status": "unavailable"}
    
    def monitor_service(self, interval=30):
        """Monitor service health."""
        print(f"👁️  Monitoring service (checking every {interval}s)...")
        print("Press Ctrl+C to stop monitoring")
        
        try:
            while True:
                status = self.get_service_status()
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                
                if status.get("status") == "healthy":
                    print(f"[{timestamp}] ✅ Service is healthy")
                else:
                    print(f"[{timestamp}] ❌ Service is unhealthy: {status}")
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped")


def setup_signal_handlers(service_manager):
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        print(f"\n📡 Received signal {signum}")
        service_manager.stop_service()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def check_dependencies():
    """Check if required dependencies are available."""
    print("🔍 Checking dependencies...")
    
    # Check Python packages
    required_packages = ["fastapi", "uvicorn", "torch", "PIL"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {missing_packages}")
        print("Please install them with: pip install -r requirements.txt")
        return False
    
    # Check if models directory exists
    models_dir = Path("models")
    if not models_dir.exists():
        print(f"⚠️  Models directory not found: {models_dir}")
        print("Creating models directory...")
        models_dir.mkdir(exist_ok=True)
    
    # Check for model files
    model_files = list(models_dir.glob("*.pth"))
    if not model_files:
        print("⚠️  No model files found in models directory")
        print("The service will use randomly initialized models")
        print("Run 'python scripts/download_models.py --dummy' to create test models")
    else:
        print(f"✅ Found {len(model_files)} model files")
    
    return True


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="AI Watermark Removal Service Manager")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker processes")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload (development)")
    parser.add_argument("--monitor", action="store_true", help="Monitor service after starting")
    parser.add_argument("--check-deps", action="store_true", help="Check dependencies only")
    
    args = parser.parse_args()
    
    # Change to project root
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    print("🎯 AI Watermark Removal Service Manager")
    print(f"Working directory: {os.getcwd()}")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    if args.check_deps:
        print("✅ All dependencies check passed")
        return
    
    # Create service manager
    service_manager = ServiceManager(
        host=args.host,
        port=args.port,
        workers=args.workers,
        reload=args.reload
    )
    
    # Setup signal handlers
    setup_signal_handlers(service_manager)
    
    # Start service
    if not service_manager.start_service():
        sys.exit(1)
    
    try:
        if args.monitor:
            # Monitor service
            service_manager.monitor_service()
        else:
            # Just wait for the service
            print("✅ Service is running")
            print(f"📖 API Documentation: http://{args.host}:{args.port}/docs")
            print(f"🔍 Health Check: http://{args.host}:{args.port}/health")
            print("Press Ctrl+C to stop")
            
            # Wait for the process
            service_manager.process.wait()
            
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
    finally:
        service_manager.stop_service()


if __name__ == "__main__":
    main()
