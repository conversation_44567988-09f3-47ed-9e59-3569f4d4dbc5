# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
uploads/*
outputs/*
temp/*
logs/*
models/*.pth
models/*.pt
models/*.onnx

# Documentation
docs/
*.md
!README.md

# Tests
tests/
pytest.ini

# Development
.env.local
.env.development
docker-compose.override.yml

# Large files
*.zip
*.tar.gz
*.rar
