"""
Advanced U-Net model for watermark removal with attention mechanisms.
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Union, Tuple, List
import numpy as np
from PIL import Image
import os

from .watermark_remover import WatermarkRemover, ResidualBlock, AttentionBlock


class UNetWatermarkRemover(WatermarkRemover):
    """Advanced U-Net based watermark remover with attention mechanisms."""
    
    def __init__(self, device: str = "cuda"):
        super().__init__(device)
        self.model = AdvancedUNet().to(self.device)
        
    def load_model(self, model_path: str = None):
        """Load pre-trained U-Net model."""
        if model_path and os.path.exists(model_path):
            checkpoint = torch.load(model_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            print(f"Loaded model from {model_path}")
        else:
            print("Using randomly initialized model. Consider training or downloading pre-trained weights.")
        
        self.model.eval()
    
    def remove_watermark(self, image: Union[np.ndarray, Image.Image]) -> Image.Image:
        """Remove watermark using U-Net model."""
        original_size = None
        if isinstance(image, Image.Image):
            original_size = image.size
            
        # Preprocess image
        input_tensor = self.preprocess_image(image, target_size=(512, 512))
        
        # Run inference
        with torch.no_grad():
            output = self.model(input_tensor)
        
        # Postprocess output
        result = self.postprocess_output(output)
        
        # Resize back to original size if needed
        if original_size:
            result = result.resize(original_size, Image.LANCZOS)
            
        return result


class AdvancedUNet(nn.Module):
    """Advanced U-Net architecture with residual connections and attention."""
    
    def __init__(self, in_channels: int = 3, out_channels: int = 3):
        super().__init__()
        
        # Encoder
        self.enc1 = self._make_encoder_block(in_channels, 64)
        self.enc2 = self._make_encoder_block(64, 128)
        self.enc3 = self._make_encoder_block(128, 256)
        self.enc4 = self._make_encoder_block(256, 512)
        
        # Bottleneck with attention
        self.bottleneck = nn.Sequential(
            ResidualBlock(512, 1024),
            AttentionBlock(1024),
            ResidualBlock(1024, 1024)
        )
        
        # Decoder
        self.dec4 = self._make_decoder_block(1024, 512)
        self.dec3 = self._make_decoder_block(512, 256)
        self.dec2 = self._make_decoder_block(256, 128)
        self.dec1 = self._make_decoder_block(128, 64)
        
        # Final output layer
        self.final = nn.Sequential(
            nn.Conv2d(64, out_channels, 1),
            nn.Sigmoid()
        )
        
        # Skip connection attention
        self.skip_att4 = AttentionBlock(512)
        self.skip_att3 = AttentionBlock(256)
        self.skip_att2 = AttentionBlock(128)
        self.skip_att1 = AttentionBlock(64)
        
    def _make_encoder_block(self, in_channels: int, out_channels: int):
        return nn.Sequential(
            ResidualBlock(in_channels, out_channels),
            ResidualBlock(out_channels, out_channels),
            nn.MaxPool2d(2)
        )
    
    def _make_decoder_block(self, in_channels: int, out_channels: int):
        return nn.Sequential(
            nn.ConvTranspose2d(in_channels, out_channels, 2, stride=2),
            ResidualBlock(out_channels * 2, out_channels),
            ResidualBlock(out_channels, out_channels)
        )
    
    def forward(self, x):
        # Encoder path
        e1 = self.enc1(x)  # 64 channels
        e2 = self.enc2(e1)  # 128 channels
        e3 = self.enc3(e2)  # 256 channels
        e4 = self.enc4(e3)  # 512 channels
        
        # Bottleneck
        b = self.bottleneck(e4)  # 1024 channels
        
        # Decoder path with skip connections
        d4 = self.dec4(b)
        skip4 = self.skip_att4(e4)
        d4 = torch.cat([d4, skip4], dim=1)
        
        d3 = self.dec3(d4)
        skip3 = self.skip_att3(e3)
        d3 = torch.cat([d3, skip3], dim=1)
        
        d2 = self.dec2(d3)
        skip2 = self.skip_att2(e2)
        d2 = torch.cat([d2, skip2], dim=1)
        
        d1 = self.dec1(d2)
        skip1 = self.skip_att1(e1)
        d1 = torch.cat([d1, skip1], dim=1)
        
        # Final output
        output = self.final(d1)
        
        return output


class WatermarkDetector(nn.Module):
    """Watermark detection network to identify watermark regions."""
    
    def __init__(self, in_channels: int = 3):
        super().__init__()
        
        self.backbone = nn.Sequential(
            nn.Conv2d(in_channels, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            nn.Conv2d(128, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
        )
        
        self.decoder = nn.Sequential(
            nn.ConvTranspose2d(256, 128, 2, stride=2),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(128, 64, 2, stride=2),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 1, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        features = self.backbone(x)
        mask = self.decoder(features)
        return mask
