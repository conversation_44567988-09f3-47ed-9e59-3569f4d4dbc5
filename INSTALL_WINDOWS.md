# 🪟 Windows 安装指南

## 🚀 快速安装 (推荐)

### 方法1: 使用批处理脚本
```cmd
# 运行Windows安装脚本
scripts\setup_windows.bat
```

### 方法2: 手动安装
```cmd
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境
venv\Scripts\activate

# 3. 升级pip (使用python -m pip)
python -m pip install --upgrade pip

# 4. 安装依赖
python -m pip install -r requirements.txt

# 5. 安装开发工具
python -m pip install pytest pytest-asyncio black isort flake8

# 6. 创建目录
mkdir models uploads outputs temp logs static

# 7. 复制环境配置
copy .env.example .env

# 8. 创建测试模型
python scripts\download_models.py --dummy
```

## 🔧 解决常见问题

### 问题1: pip升级失败
```
ERROR: To modify pip, please run the following command:
C:\...\python.exe -m pip install --upgrade pip
```

**解决方案**: 使用 `python -m pip` 而不是直接使用 `pip`
```cmd
python -m pip install --upgrade pip
```

### 问题2: 权限错误
如果遇到权限问题，尝试：
```cmd
# 以管理员身份运行命令提示符
# 或者添加 --user 参数
python -m pip install --user -r requirements.txt
```

### 问题3: CUDA不可用
如果没有NVIDIA GPU或CUDA：
```cmd
# 编辑 .env 文件，设置使用CPU
echo DEVICE=cpu >> .env
```

### 问题4: 长路径问题
Windows可能有路径长度限制：
```cmd
# 启用长路径支持 (需要管理员权限)
reg add HKLM\SYSTEM\CurrentControlSet\Control\FileSystem /v LongPathsEnabled /t REG_DWORD /d 1
```

## 🚀 启动服务

### 开发模式
```cmd
# 激活虚拟环境
venv\Scripts\activate

# 启动服务
python scripts\start_service.py --reload

# 或使用uvicorn直接启动
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 生产模式
```cmd
# 使用gunicorn (需要先安装)
python -m pip install gunicorn
gunicorn app.main:app -w 2 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 🧪 验证安装

```cmd
# 1. 检查Python导入
python -c "import torch; import fastapi; import PIL; print('✅ 导入成功')"

# 2. 启动服务
python scripts\start_service.py

# 3. 测试健康检查 (新开命令窗口)
curl http://localhost:8000/health
# 或在浏览器访问: http://localhost:8000/health

# 4. 查看API文档
# 浏览器访问: http://localhost:8000/docs
```

## 🐳 Docker 替代方案

如果本地安装有问题，可以使用Docker：

```cmd
# 安装Docker Desktop for Windows
# 然后运行:
docker-compose up --build
```

## 📝 环境配置

编辑 `.env` 文件：
```env
# 基本设置
DEBUG=true
DEVICE=cuda  # 或 cpu
PORT=8000

# 如果使用CPU
DEVICE=cpu
TORCH_NUM_THREADS=4

# 如果使用GPU
DEVICE=cuda
CUDA_VISIBLE_DEVICES=0
```

## 🆘 获取帮助

如果仍有问题：

1. **检查Python版本**: `python --version` (需要3.9+)
2. **检查pip版本**: `python -m pip --version`
3. **查看错误日志**: 检查命令输出的详细错误信息
4. **重新安装**: 删除 `venv` 文件夹，重新运行安装

## 🎯 下一步

安装完成后：
1. 访问 http://localhost:8000/docs 查看API文档
2. 运行 `python examples\client_example.py` 测试功能
3. 查看 `README.md` 了解更多功能
