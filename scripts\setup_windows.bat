@echo off
echo 🚀 AI Watermark Removal Service - Windows Setup
echo ================================================

:: Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.9+ first.
    pause
    exit /b 1
)

echo ✅ Python found
python --version

:: Create virtual environment
echo.
echo 🏗️  Creating virtual environment...
if exist venv (
    echo ✅ Virtual environment already exists
) else (
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created
)

:: Activate virtual environment and install dependencies
echo.
echo 📦 Installing dependencies...
call venv\Scripts\activate.bat

:: Upgrade pip using python -m pip
echo 🔧 Upgrading pip...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo ⚠️  Pip upgrade failed, continuing anyway...
)

:: Install requirements
echo 📋 Installing requirements...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install requirements
    pause
    exit /b 1
)

:: Install development dependencies
echo 🛠️  Installing development dependencies...
python -m pip install pytest pytest-asyncio black isort flake8 jupyter
if errorlevel 1 (
    echo ⚠️  Some development dependencies failed to install
)

:: Create directories
echo.
echo 📁 Creating directories...
if not exist models mkdir models
if not exist uploads mkdir uploads
if not exist outputs mkdir outputs
if not exist temp mkdir temp
if not exist logs mkdir logs
if not exist static mkdir static
echo ✅ Directories created

:: Setup environment file
echo.
echo 📝 Setting up environment file...
if not exist .env (
    if exist .env.example (
        copy .env.example .env
        echo ✅ .env file created from .env.example
        echo ⚠️  Please review and update .env file with your settings
    ) else (
        echo ⚠️  .env.example not found
    )
) else (
    echo ✅ .env file already exists
)

:: Create test models
echo.
echo 🤖 Creating test models...
python scripts\download_models.py --dummy
if errorlevel 1 (
    echo ⚠️  Failed to create test models
)

:: Test installation
echo.
echo 🧪 Testing installation...
python -c "import torch; import fastapi; import PIL; print('✅ All imports successful')"
if errorlevel 1 (
    echo ❌ Installation test failed
    pause
    exit /b 1
)

echo.
echo ================================================
echo 🎉 Setup completed successfully!
echo ================================================
echo.
echo Next steps:
echo 1. Activate virtual environment: venv\Scripts\activate
echo 2. Review configuration: edit .env
echo 3. Start the service: python scripts\start_service.py --reload
echo 4. Access API docs: http://localhost:8000/docs
echo.
echo For more commands, see Makefile or run: python scripts\start_service.py --help
echo.
pause
