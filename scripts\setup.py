#!/usr/bin/env python3
"""
Setup script for AI Watermark Removal Service.
"""
import os
import sys
import subprocess
import platform
from pathlib import Path
import argparse


def run_command(command, description, check=True):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(f"   {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stderr:
            print(f"   Error: {e.stderr.strip()}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    
    if version.major != 3 or version.minor < 9:
        print(f"❌ Python 3.9+ required, found {version.major}.{version.minor}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True


def check_system_dependencies():
    """Check system dependencies."""
    print("🔍 Checking system dependencies...")
    
    # Check for CUDA if on Linux/Windows
    if platform.system() in ["Linux", "Windows"]:
        cuda_available = run_command("nvidia-smi", "Checking CUDA/GPU", check=False)
        if cuda_available:
            print("✅ CUDA/GPU detected")
        else:
            print("⚠️  CUDA/GPU not detected - will use CPU")
    
    # Check for required system packages
    if platform.system() == "Linux":
        packages = ["libgl1-mesa-glx", "libglib2.0-0"]
        for package in packages:
            if run_command(f"dpkg -l | grep {package}", f"Checking {package}", check=False):
                print(f"✅ {package} found")
            else:
                print(f"⚠️  {package} not found - may need to install")
    
    return True


def setup_virtual_environment():
    """Setup Python virtual environment."""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    print("🏗️  Creating virtual environment...")
    if not run_command(f"{sys.executable} -m venv venv", "Creating virtual environment"):
        return False
    
    print("✅ Virtual environment created")
    return True


def install_dependencies(dev=False):
    """Install Python dependencies."""
    venv_python = "venv/bin/python" if platform.system() != "Windows" else "venv\\Scripts\\python.exe"
    venv_pip = "venv/bin/pip" if platform.system() != "Windows" else "venv\\Scripts\\pip.exe"
    
    # Upgrade pip
    if not run_command(f"{venv_pip} install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command(f"{venv_pip} install -r requirements.txt", "Installing dependencies"):
        return False
    
    # Install development dependencies
    if dev:
        dev_packages = ["pytest", "pytest-asyncio", "black", "isort", "flake8", "jupyter"]
        for package in dev_packages:
            if not run_command(f"{venv_pip} install {package}", f"Installing {package}"):
                print(f"⚠️  Failed to install {package}")
    
    return True


def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = [
        "models",
        "uploads", 
        "outputs",
        "temp",
        "logs",
        "static"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created {directory}/")
    
    return True


def setup_environment_file():
    """Setup environment configuration file."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_example.exists():
        print("📝 Creating .env from .env.example...")
        env_file.write_text(env_example.read_text())
        print("✅ .env file created")
        print("⚠️  Please review and update .env file with your settings")
    else:
        print("⚠️  .env.example not found, skipping .env creation")
    
    return True


def download_models(dummy=True):
    """Download or create model files."""
    print("🤖 Setting up AI models...")
    
    models_dir = Path("models")
    existing_models = list(models_dir.glob("*.pth"))
    
    if existing_models:
        print(f"✅ Found {len(existing_models)} existing model files")
        return True
    
    if dummy:
        print("📦 Creating dummy models for testing...")
        if run_command("python scripts/download_models.py --dummy", "Creating dummy models"):
            print("✅ Dummy models created")
            return True
    else:
        print("📦 Downloading pre-trained models...")
        if run_command("python scripts/download_models.py", "Downloading models"):
            print("✅ Models downloaded")
            return True
    
    print("⚠️  Model setup failed - service will use random weights")
    return False


def run_tests():
    """Run basic tests to verify setup."""
    print("🧪 Running setup verification tests...")
    
    # Test imports
    test_script = """
import sys
try:
    import torch
    import fastapi
    import PIL
    from app.core.config import settings
    print("✅ All imports successful")
    sys.exit(0)
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
"""
    
    with open("test_setup.py", "w") as f:
        f.write(test_script)
    
    venv_python = "venv/bin/python" if platform.system() != "Windows" else "venv\\Scripts\\python.exe"
    success = run_command(f"{venv_python} test_setup.py", "Testing imports")
    
    # Cleanup
    Path("test_setup.py").unlink(missing_ok=True)
    
    return success


def print_next_steps():
    """Print next steps for the user."""
    print("\n" + "="*60)
    print("🎉 Setup completed successfully!")
    print("="*60)
    print("\nNext steps:")
    print("1. Activate virtual environment:")
    
    if platform.system() == "Windows":
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    
    print("\n2. Review configuration:")
    print("   edit .env")
    
    print("\n3. Start the service:")
    print("   make start-dev")
    print("   # or")
    print("   python scripts/start_service.py --reload")
    
    print("\n4. Access the service:")
    print("   API Documentation: http://localhost:8000/docs")
    print("   Health Check: http://localhost:8000/health")
    
    print("\n5. Run tests:")
    print("   make test")
    
    print("\nFor more commands, run: make help")


def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="Setup AI Watermark Removal Service")
    parser.add_argument("--dev", action="store_true", help="Install development dependencies")
    parser.add_argument("--no-models", action="store_true", help="Skip model download/creation")
    parser.add_argument("--real-models", action="store_true", help="Download real models instead of dummy")
    parser.add_argument("--skip-tests", action="store_true", help="Skip verification tests")
    
    args = parser.parse_args()
    
    print("🚀 AI Watermark Removal Service Setup")
    print("="*50)
    
    # Change to project root
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    print(f"Working directory: {os.getcwd()}")
    
    success = True
    
    # Check Python version
    if not check_python_version():
        success = False
    
    # Check system dependencies
    if not check_system_dependencies():
        print("⚠️  Some system dependencies missing - continuing anyway")
    
    # Setup virtual environment
    if success and not setup_virtual_environment():
        success = False
    
    # Install dependencies
    if success and not install_dependencies(dev=args.dev):
        success = False
    
    # Create directories
    if success and not create_directories():
        success = False
    
    # Setup environment file
    if success and not setup_environment_file():
        success = False
    
    # Download/create models
    if success and not args.no_models:
        download_models(dummy=not args.real_models)
    
    # Run verification tests
    if success and not args.skip_tests:
        if not run_tests():
            print("⚠️  Some tests failed - setup may be incomplete")
    
    if success:
        print_next_steps()
        return 0
    else:
        print("\n❌ Setup failed - please check errors above")
        return 1


if __name__ == "__main__":
    sys.exit(main())
