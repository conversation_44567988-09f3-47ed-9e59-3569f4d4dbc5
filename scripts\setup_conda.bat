@echo off
echo 🚀 AI Watermark Removal Service - Conda Setup (Windows)
echo ========================================================

:: Check if conda is available
conda --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Conda not found. Please install Anaconda or Miniconda first.
    echo    Download from: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

echo ✅ Conda found
conda --version

:: Check if mamba is available (faster package resolver)
mamba --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Mamba not found, using conda (slower but reliable)
    set CONDA_CMD=conda
) else (
    echo ✅ Mamba found (faster package resolution)
    set CONDA_CMD=mamba
)

:: Create conda environment
echo.
echo 🏗️  Creating conda environment from environment.yml...

:: Check if environment already exists
conda env list | findstr "watermark-removal" >nul
if not errorlevel 1 (
    echo ✅ Environment 'watermark-removal' already exists
    goto :install_additional
)

:: Create environment
%CONDA_CMD% env create -f environment.yml
if errorlevel 1 (
    echo ❌ Failed to create conda environment
    echo    Trying with conda instead of mamba...
    conda env create -f environment.yml
    if errorlevel 1 (
        echo ❌ Failed to create environment with conda too
        pause
        exit /b 1
    )
)
echo ✅ Conda environment created successfully

:install_additional
:: Install additional packages that might not be in environment.yml
echo.
echo 📦 Installing additional packages...
conda run -n watermark-removal pip install transformers diffusers albumentations
if errorlevel 1 (
    echo ⚠️  Some additional packages failed to install
)

:: Create directories
echo.
echo 📁 Creating directories...
if not exist models mkdir models
if not exist uploads mkdir uploads
if not exist outputs mkdir outputs
if not exist temp mkdir temp
if not exist logs mkdir logs
if not exist static mkdir static
echo ✅ Directories created

:: Setup environment file
echo.
echo 📝 Setting up environment file...
if not exist .env (
    if exist .env.example (
        copy .env.example .env
        echo ✅ .env file created from .env.example
        echo ⚠️  Please review and update .env file with your settings
    ) else (
        echo ⚠️  .env.example not found
    )
) else (
    echo ✅ .env file already exists
)

:: Create activation script
echo.
echo 📝 Creating activation script...
(
echo @echo off
echo echo Activating watermark-removal conda environment...
echo call conda activate watermark-removal
echo echo ✅ Environment activated!
echo echo.
echo echo Available commands:
echo echo   python scripts\start_service.py --reload  # Start development server
echo echo   python scripts\download_models.py --dummy # Create test models
echo echo   python examples\client_example.py         # Run example client
echo echo.
echo cmd /k
) > activate_env.bat
echo ✅ Created activate_env.bat

:: Create test models
echo.
echo 🤖 Creating test models...
conda run -n watermark-removal python scripts\download_models.py --dummy
if errorlevel 1 (
    echo ⚠️  Failed to create test models
)

:: Test installation
echo.
echo 🧪 Testing installation...
conda run -n watermark-removal python -c "import torch; import fastapi; import PIL; import cv2; print('✅ All imports successful'); print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')"
if errorlevel 1 (
    echo ❌ Installation test failed
    pause
    exit /b 1
)

echo.
echo ========================================================
echo 🎉 Conda setup completed successfully!
echo ========================================================
echo.
echo Next steps:
echo 1. Activate environment: conda activate watermark-removal
echo    Or run: activate_env.bat
echo.
echo 2. Review configuration: edit .env
echo.
echo 3. Start the service: python scripts\start_service.py --reload
echo.
echo 4. Access the service:
echo    - API Documentation: http://localhost:8000/docs
echo    - Health Check: http://localhost:8000/health
echo.
echo 5. Run tests: pytest tests/
echo.
echo For quick start, just run: activate_env.bat
echo.
pause
