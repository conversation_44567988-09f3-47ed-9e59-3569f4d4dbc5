#!/usr/bin/env python3
"""
Conda-based setup script for AI Watermark Removal Service.
"""
import os
import sys
import subprocess
import platform
from pathlib import Path
import argparse


def run_command(command, description, check=True, shell=True):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=shell, check=check, capture_output=True, text=True)
        if result.stdout:
            print(f"   {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stderr:
            print(f"   Error: {e.stderr.strip()}")
        return False


def check_conda():
    """Check if conda is available."""
    print("🐍 Checking conda installation...")
    
    # Try different conda commands
    conda_commands = ["conda", "mamba"]  # mamba is faster if available
    
    for cmd in conda_commands:
        try:
            result = subprocess.run([cmd, "--version"], capture_output=True, text=True, check=True)
            print(f"✅ Found {cmd}: {result.stdout.strip()}")
            return cmd
        except (subprocess.CalledProcessError, FileNotFoundError):
            continue
    
    print("❌ Conda not found. Please install Anaconda or Miniconda first.")
    print("   Download from: https://docs.conda.io/en/latest/miniconda.html")
    return None


def create_conda_environment(conda_cmd, env_name="watermark-removal"):
    """Create conda environment from environment.yml."""
    print(f"🏗️  Creating conda environment: {env_name}")
    
    # Check if environment already exists
    result = subprocess.run([conda_cmd, "env", "list"], capture_output=True, text=True)
    if env_name in result.stdout:
        print(f"✅ Environment '{env_name}' already exists")
        return True
    
    # Create environment from file
    if Path("environment.yml").exists():
        cmd = [conda_cmd, "env", "create", "-f", "environment.yml"]
        if run_command(cmd, f"Creating environment from environment.yml", shell=False):
            print(f"✅ Environment '{env_name}' created successfully")
            return True
    else:
        print("❌ environment.yml not found")
        return False
    
    return False


def activate_environment_instructions(env_name="watermark-removal"):
    """Print environment activation instructions."""
    print(f"\n📝 To activate the environment, run:")
    if platform.system() == "Windows":
        print(f"   conda activate {env_name}")
    else:
        print(f"   conda activate {env_name}")


def install_additional_packages(conda_cmd, env_name="watermark-removal"):
    """Install additional packages that might not be in environment.yml."""
    print("📦 Installing additional packages...")
    
    # Packages that might need special handling
    additional_packages = [
        "transformers",
        "diffusers", 
        "albumentations"
    ]
    
    for package in additional_packages:
        cmd = [conda_cmd, "run", "-n", env_name, "pip", "install", package]
        if not run_command(cmd, f"Installing {package}", shell=False, check=False):
            print(f"⚠️  Failed to install {package}, will try later")


def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = [
        "models",
        "uploads", 
        "outputs",
        "temp",
        "logs",
        "static"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created {directory}/")
    
    return True


def setup_environment_file():
    """Setup environment configuration file."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_example.exists():
        print("📝 Creating .env from .env.example...")
        env_file.write_text(env_example.read_text())
        print("✅ .env file created")
        print("⚠️  Please review and update .env file with your settings")
    else:
        print("⚠️  .env.example not found, skipping .env creation")
    
    return True


def create_conda_activation_script():
    """Create conda activation script for easy use."""
    if platform.system() == "Windows":
        script_content = f"""@echo off
echo Activating watermark-removal conda environment...
call conda activate watermark-removal
echo ✅ Environment activated!
echo.
echo Available commands:
echo   python scripts\\start_service.py --reload  # Start development server
echo   python scripts\\download_models.py --dummy # Create test models
echo   python examples\\client_example.py         # Run example client
echo.
cmd /k
"""
        script_path = Path("activate_env.bat")
    else:
        script_content = f"""#!/bin/bash
echo "Activating watermark-removal conda environment..."
conda activate watermark-removal
echo "✅ Environment activated!"
echo ""
echo "Available commands:"
echo "  python scripts/start_service.py --reload  # Start development server"
echo "  python scripts/download_models.py --dummy # Create test models"
echo "  python examples/client_example.py         # Run example client"
echo ""
exec bash
"""
        script_path = Path("activate_env.sh")
    
    script_path.write_text(script_content)
    if not platform.system() == "Windows":
        os.chmod(script_path, 0o755)
    
    print(f"✅ Created activation script: {script_path}")
    return True


def download_models(conda_cmd, env_name="watermark-removal"):
    """Download or create model files."""
    print("🤖 Setting up AI models...")
    
    models_dir = Path("models")
    existing_models = list(models_dir.glob("*.pth"))
    
    if existing_models:
        print(f"✅ Found {len(existing_models)} existing model files")
        return True
    
    print("📦 Creating dummy models for testing...")
    cmd = [conda_cmd, "run", "-n", env_name, "python", "scripts/download_models.py", "--dummy"]
    if run_command(cmd, "Creating dummy models", shell=False):
        print("✅ Dummy models created")
        return True
    
    print("⚠️  Model setup failed - service will use random weights")
    return False


def test_installation(conda_cmd, env_name="watermark-removal"):
    """Test the installation."""
    print("🧪 Testing installation...")
    
    test_script = """
import sys
try:
    import torch
    import fastapi
    import PIL
    import cv2
    import numpy as np
    from app.core.config import settings
    print("✅ All imports successful")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
    sys.exit(0)
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
"""
    
    # Write test script
    test_file = Path("test_conda_setup.py")
    test_file.write_text(test_script)
    
    # Run test
    cmd = [conda_cmd, "run", "-n", env_name, "python", "test_conda_setup.py"]
    success = run_command(cmd, "Testing imports", shell=False)
    
    # Cleanup
    test_file.unlink(missing_ok=True)
    
    return success


def print_next_steps(env_name="watermark-removal"):
    """Print next steps for the user."""
    print("\n" + "="*60)
    print("🎉 Conda setup completed successfully!")
    print("="*60)
    print("\nNext steps:")
    print(f"1. Activate conda environment:")
    print(f"   conda activate {env_name}")
    
    print("\n2. Review configuration:")
    print("   edit .env")
    
    print("\n3. Start the service:")
    print("   python scripts/start_service.py --reload")
    
    print("\n4. Access the service:")
    print("   API Documentation: http://localhost:8000/docs")
    print("   Health Check: http://localhost:8000/health")
    
    print("\n5. Run tests:")
    print("   pytest tests/")
    
    print(f"\nQuick activation script:")
    if platform.system() == "Windows":
        print("   activate_env.bat")
    else:
        print("   ./activate_env.sh")
    
    print("\nFor more commands, see README.md")


def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="Setup AI Watermark Removal Service with Conda")
    parser.add_argument("--env-name", default="watermark-removal", help="Conda environment name")
    parser.add_argument("--skip-models", action="store_true", help="Skip model download/creation")
    parser.add_argument("--skip-tests", action="store_true", help="Skip verification tests")
    parser.add_argument("--use-mamba", action="store_true", help="Prefer mamba over conda")
    
    args = parser.parse_args()
    
    print("🚀 AI Watermark Removal Service - Conda Setup")
    print("="*50)
    
    # Change to project root
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    print(f"Working directory: {os.getcwd()}")
    
    success = True
    
    # Check conda availability
    conda_cmd = check_conda()
    if not conda_cmd:
        return 1
    
    # Prefer mamba if requested and available
    if args.use_mamba and conda_cmd != "mamba":
        try:
            subprocess.run(["mamba", "--version"], capture_output=True, check=True)
            conda_cmd = "mamba"
            print("✅ Using mamba for faster package resolution")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  Mamba not available, using conda")
    
    # Create conda environment
    if success and not create_conda_environment(conda_cmd, args.env_name):
        success = False
    
    # Install additional packages
    if success:
        install_additional_packages(conda_cmd, args.env_name)
    
    # Create directories
    if success and not create_directories():
        success = False
    
    # Setup environment file
    if success and not setup_environment_file():
        success = False
    
    # Create activation script
    if success:
        create_conda_activation_script()
    
    # Download/create models
    if success and not args.skip_models:
        download_models(conda_cmd, args.env_name)
    
    # Test installation
    if success and not args.skip_tests:
        if not test_installation(conda_cmd, args.env_name):
            print("⚠️  Some tests failed - setup may be incomplete")
    
    if success:
        print_next_steps(args.env_name)
        return 0
    else:
        print("\n❌ Setup failed - please check errors above")
        return 1


if __name__ == "__main__":
    sys.exit(main())
