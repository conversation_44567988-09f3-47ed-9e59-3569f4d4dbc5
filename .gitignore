# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Project specific
uploads/*
!uploads/.gitkeep
outputs/*
!outputs/.gitkeep
temp/*
!temp/.gitkeep
logs/*
!logs/.gitkeep

# Model files
models/*.pth
models/*.pt
models/*.onnx
models/*.pkl
models/*.bin

# Data files
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.webp
*.svg

# Except example files
!examples/*.jpg
!examples/*.png

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Backup files
*.bak
*.backup
*.old

# Temporary files
*.tmp
*.temp

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Configuration overrides
docker-compose.override.yml
.env.local
.env.production
.env.development

# SSL certificates
ssl/
*.pem
*.key
*.crt

# Database
*.db
*.sqlite

# Redis dump
dump.rdb

# Monitoring data
prometheus_data/
grafana_data/

# Large files
*.bin
*.dat
*.h5
*.hdf5

# Jupyter checkpoints
.ipynb_checkpoints/

# PyTorch
*.pth
*.pt

# TensorFlow
*.pb
*.tflite

# ONNX
*.onnx

# Weights & Biases
wandb/

# MLflow
mlruns/

# DVC
.dvc/
.dvcignore

# Hydra
outputs/
multirun/
.hydra/
