"""
Watermark removal API endpoints.
"""
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from fastapi.responses import StreamingResponse
from typing import Optional, List
import io
import uuid
import asyncio
from datetime import datetime

from app.core.config import settings
from app.services.watermark_service import WatermarkService
from app.schemas.watermark import (
    WatermarkRemovalRequest,
    WatermarkRemovalResponse,
    ProcessingStatus
)
from app.api.dependencies import get_watermark_service

router = APIRouter()


@router.post("/remove", response_model=WatermarkRemovalResponse)
async def remove_watermark(
    image: UploadFile = File(..., description="Image file with watermark"),
    model_type: str = Form("unet", description="Model type: unet, diffusion"),
    enhance_quality: bool = Form(True, description="Apply quality enhancement"),
    return_format: str = Form("png", description="Output format: png, jpg, webp"),
    service: WatermarkService = Depends(get_watermark_service)
):
    """
    Remove watermark from uploaded image.
    
    - **image**: Image file (JPG, PNG, WebP supported)
    - **model_type**: AI model to use (unet, diffusion)
    - **enhance_quality**: Whether to apply post-processing enhancement
    - **return_format**: Output image format
    """
    # Validate file
    if not image.content_type or not image.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    if image.size > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=413, 
            detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE // (1024*1024)}MB"
        )
    
    # Validate format
    file_ext = image.filename.split('.')[-1].lower() if image.filename else ''
    if file_ext not in settings.SUPPORTED_FORMATS:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported format. Supported: {settings.SUPPORTED_FORMATS}"
        )
    
    try:
        # Read image data
        image_data = await image.read()
        
        # Create processing request
        request = WatermarkRemovalRequest(
            model_type=model_type,
            enhance_quality=enhance_quality,
            output_format=return_format
        )
        
        # Process image
        result = await service.remove_watermark(image_data, request)
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")


@router.post("/remove-stream")
async def remove_watermark_stream(
    image: UploadFile = File(...),
    model_type: str = Form("unet"),
    enhance_quality: bool = Form(True),
    return_format: str = Form("png"),
    service: WatermarkService = Depends(get_watermark_service)
):
    """
    Remove watermark and return image directly as stream.
    """
    # Validate file (same as above)
    if not image.content_type or not image.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    if image.size > settings.MAX_FILE_SIZE:
        raise HTTPException(status_code=413, detail="File too large")
    
    try:
        # Read and process image
        image_data = await image.read()
        
        request = WatermarkRemovalRequest(
            model_type=model_type,
            enhance_quality=enhance_quality,
            output_format=return_format
        )
        
        result = await service.remove_watermark(image_data, request)
        
        # Convert base64 to bytes
        import base64
        image_bytes = base64.b64decode(result.processed_image)
        
        # Determine content type
        content_type_map = {
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'webp': 'image/webp'
        }
        content_type = content_type_map.get(return_format, 'image/png')
        
        # Return as streaming response
        return StreamingResponse(
            io.BytesIO(image_bytes),
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename=watermark_removed.{return_format}"
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")


@router.post("/analyze")
async def analyze_watermark(
    image: UploadFile = File(...),
    service: WatermarkService = Depends(get_watermark_service)
):
    """
    Analyze image to detect watermark regions and characteristics.
    """
    if not image.content_type or not image.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    try:
        image_data = await image.read()
        analysis = await service.analyze_watermark(image_data)
        
        return {
            "watermark_detected": analysis.get("detected", False),
            "confidence": analysis.get("confidence", 0.0),
            "regions": analysis.get("regions", []),
            "characteristics": analysis.get("characteristics", {}),
            "recommended_model": analysis.get("recommended_model", "unet")
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.get("/models")
async def get_available_models(
    service: WatermarkService = Depends(get_watermark_service)
):
    """
    Get information about available AI models.
    """
    try:
        models = await service.get_model_info()
        return {
            "available_models": models,
            "default_model": "unet",
            "recommended_for_speed": "unet",
            "recommended_for_quality": "diffusion"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get model info: {str(e)}")


@router.post("/compare")
async def compare_models(
    image: UploadFile = File(...),
    models: List[str] = Form(["unet", "diffusion"]),
    service: WatermarkService = Depends(get_watermark_service)
):
    """
    Compare results from different models on the same image.
    """
    if not image.content_type or not image.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    try:
        image_data = await image.read()
        
        # Process with each model
        results = {}
        for model_type in models:
            request = WatermarkRemovalRequest(
                model_type=model_type,
                enhance_quality=True,
                output_format="png"
            )
            
            result = await service.remove_watermark(image_data, request)
            results[model_type] = {
                "processed_image": result.processed_image,
                "processing_time": result.processing_time,
                "confidence": result.confidence
            }
        
        return {
            "comparison_id": str(uuid.uuid4()),
            "timestamp": datetime.utcnow().isoformat(),
            "results": results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Comparison failed: {str(e)}")
