name: watermark-removal
channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults

dependencies:
  # Python
  - python=3.9

  # Deep Learning
  - pytorch>=2.0.0
  - torchvision
  - torchaudio
  - pytorch-cuda=11.8  # For CUDA support
  
  # Computer Vision
  - opencv
  - pillow>=10.0.0
  - scikit-image
  - imageio
  
  # Web Framework
  - fastapi
  - uvicorn
  - python-multipart
  - aiofiles
  - httpx
  - jinja2
  
  # Data Science
  - numpy>=1.24.0
  - scipy
  - pandas
  - matplotlib
  - seaborn
  
  # Utilities
  - pydantic>=2.0.0
  - python-dotenv
  - loguru
  - typer
  - rich
  - tqdm
  - requests
  
  # Development Tools
  - pytest
  - pytest-asyncio
  - black
  - isort
  - flake8
  - jupyter
  - jupyterlab
  
  # System
  - psutil
  
  # pip dependencies (not available in conda)
  - pip
  - pip:
    - transformers>=4.30.0
    - diffusers>=0.20.0
    - albumentations>=1.3.0
    - gunicorn
    - redis
    - celery
