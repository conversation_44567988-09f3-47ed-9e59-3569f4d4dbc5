"""
Advanced image processing utilities for watermark removal.
"""
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from typing import Union, Tuple, Optional, List
import torch
import torchvision.transforms as transforms
from skimage import restoration, filters, morphology
from scipy import ndimage
import albumentations as A


class ImageProcessor:
    """Advanced image processing for watermark removal preprocessing and postprocessing."""
    
    def __init__(self):
        self.supported_formats = ['jpg', 'jpeg', 'png', 'webp', 'bmp', 'tiff']
        
    def load_image(self, image_path: str) -> Image.Image:
        """Load image from file path."""
        try:
            image = Image.open(image_path)
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            return image
        except Exception as e:
            raise ValueError(f"Failed to load image: {e}")
    
    def save_image(self, image: Image.Image, output_path: str, quality: int = 95):
        """Save image with specified quality."""
        try:
            # Determine format from extension
            format_map = {
                '.jpg': 'JPEG', '.jpeg': 'JPEG',
                '.png': 'PNG', '.webp': 'WebP',
                '.bmp': 'BMP', '.tiff': 'TIFF'
            }
            
            ext = output_path.lower().split('.')[-1]
            if f'.{ext}' in format_map:
                save_format = format_map[f'.{ext}']
                if save_format == 'JPEG':
                    image.save(output_path, format=save_format, quality=quality, optimize=True)
                else:
                    image.save(output_path, format=save_format, optimize=True)
            else:
                image.save(output_path, quality=quality, optimize=True)
        except Exception as e:
            raise ValueError(f"Failed to save image: {e}")
    
    def resize_image(self, image: Image.Image, target_size: Tuple[int, int], 
                    method: str = 'lanczos') -> Image.Image:
        """Resize image with specified method."""
        resize_methods = {
            'lanczos': Image.LANCZOS,
            'bicubic': Image.BICUBIC,
            'bilinear': Image.BILINEAR,
            'nearest': Image.NEAREST
        }
        
        method_enum = resize_methods.get(method.lower(), Image.LANCZOS)
        return image.resize(target_size, method_enum)
    
    def smart_resize(self, image: Image.Image, max_size: int = 2048) -> Image.Image:
        """Smart resize that maintains aspect ratio."""
        width, height = image.size
        
        if max(width, height) <= max_size:
            return image
        
        if width > height:
            new_width = max_size
            new_height = int(height * max_size / width)
        else:
            new_height = max_size
            new_width = int(width * max_size / height)
        
        return self.resize_image(image, (new_width, new_height))
    
    def preprocess_for_model(self, image: Image.Image, 
                           target_size: Tuple[int, int] = (512, 512)) -> torch.Tensor:
        """Preprocess image for model input."""
        # Convert to numpy array
        img_array = np.array(image)
        
        # Apply preprocessing transforms
        transform = A.Compose([
            A.Resize(target_size[0], target_size[1]),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ])
        
        transformed = transform(image=img_array)
        img_tensor = torch.from_numpy(transformed['image']).permute(2, 0, 1).float()
        
        return img_tensor.unsqueeze(0)  # Add batch dimension
    
    def postprocess_from_model(self, tensor: torch.Tensor, 
                             original_size: Optional[Tuple[int, int]] = None) -> Image.Image:
        """Postprocess model output to image."""
        # Remove batch dimension and convert to numpy
        if tensor.dim() == 4:
            tensor = tensor.squeeze(0)
        
        # Denormalize
        tensor = tensor * torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1) + \
                torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        
        # Clamp values
        tensor = torch.clamp(tensor, 0, 1)
        
        # Convert to PIL Image
        img_array = tensor.permute(1, 2, 0).cpu().numpy()
        img_array = (img_array * 255).astype(np.uint8)
        image = Image.fromarray(img_array)
        
        # Resize to original size if specified
        if original_size:
            image = self.resize_image(image, original_size)
        
        return image
    
    def detect_watermark_regions(self, image: Image.Image) -> np.ndarray:
        """Detect potential watermark regions using image analysis."""
        img_array = np.array(image.convert('L'))  # Convert to grayscale
        
        # Apply edge detection
        edges = cv2.Canny(img_array, 50, 150)
        
        # Apply morphological operations to connect edges
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Create mask for potential watermark regions
        mask = np.zeros_like(img_array)
        for contour in contours:
            area = cv2.contourArea(contour)
            if 100 < area < 10000:  # Filter by area
                cv2.fillPoly(mask, [contour], 255)
        
        return mask
    
    def enhance_contrast(self, image: Image.Image, factor: float = 1.2) -> Image.Image:
        """Enhance image contrast."""
        enhancer = ImageEnhance.Contrast(image)
        return enhancer.enhance(factor)
    
    def enhance_sharpness(self, image: Image.Image, factor: float = 1.1) -> Image.Image:
        """Enhance image sharpness."""
        enhancer = ImageEnhance.Sharpness(image)
        return enhancer.enhance(factor)
    
    def reduce_noise(self, image: Image.Image, method: str = 'bilateral') -> Image.Image:
        """Reduce image noise using various methods."""
        img_array = np.array(image)
        
        if method == 'bilateral':
            # Bilateral filter preserves edges while reducing noise
            denoised = cv2.bilateralFilter(img_array, 9, 75, 75)
        elif method == 'gaussian':
            # Gaussian blur
            denoised = cv2.GaussianBlur(img_array, (5, 5), 0)
        elif method == 'median':
            # Median filter
            denoised = cv2.medianBlur(img_array, 5)
        elif method == 'nlm':
            # Non-local means denoising
            denoised = cv2.fastNlMeansDenoisingColored(img_array, None, 10, 10, 7, 21)
        else:
            denoised = img_array
        
        return Image.fromarray(denoised)
    
    def apply_unsharp_mask(self, image: Image.Image, radius: float = 1.0, 
                          amount: float = 1.0) -> Image.Image:
        """Apply unsharp mask for image enhancement."""
        img_array = np.array(image).astype(np.float32) / 255.0
        
        # Create Gaussian blur
        blurred = filters.gaussian(img_array, sigma=radius, multichannel=True)
        
        # Create unsharp mask
        unsharp_mask = img_array + amount * (img_array - blurred)
        
        # Clip values and convert back
        unsharp_mask = np.clip(unsharp_mask, 0, 1)
        result = (unsharp_mask * 255).astype(np.uint8)
        
        return Image.fromarray(result)
    
    def correct_gamma(self, image: Image.Image, gamma: float = 1.0) -> Image.Image:
        """Apply gamma correction."""
        if gamma == 1.0:
            return image
            
        img_array = np.array(image).astype(np.float32) / 255.0
        corrected = np.power(img_array, gamma)
        result = (corrected * 255).astype(np.uint8)
        
        return Image.fromarray(result)
