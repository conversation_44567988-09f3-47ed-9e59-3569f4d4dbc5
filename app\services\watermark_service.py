"""
Core watermark removal service.
"""
import asyncio
import base64
import io
import time
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from PIL import Image
import numpy as np
from loguru import logger

from app.schemas.watermark import (
    WatermarkRemovalRequest,
    WatermarkRemovalResponse,
    ProcessingStatus,
    WatermarkAnalysis
)
from app.services.model_manager import ModelManager
from app.utils.image_processor import ImageProcessor
from app.utils.quality_enhancer import QualityEnhancer


class WatermarkService:
    """Service for watermark removal operations."""
    
    def __init__(self, model_manager: ModelManager):
        self.model_manager = model_manager
        self.image_processor = ImageProcessor()
        self.quality_enhancer = QualityEnhancer(device=str(model_manager.device))
        
    async def remove_watermark(
        self, 
        image_data: bytes, 
        request: WatermarkRemovalRequest
    ) -> WatermarkRemovalResponse:
        """Remove watermark from image data."""
        request_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            logger.info(f"Processing watermark removal request {request_id}")
            
            # Load image from bytes
            image = Image.open(io.BytesIO(image_data))
            original_size = image.size
            
            # Preprocess image
            processed_image = self._preprocess_image(image)
            
            # Get the appropriate model
            model = self.model_manager.get_model(request.model_type)
            
            # Remove watermark using AI model
            result_image = model.remove_watermark(processed_image)
            
            # Post-process if requested
            if request.enhance_quality:
                result_image = self.quality_enhancer.enhance_image(
                    result_image,
                    enhance_contrast=True,
                    enhance_sharpness=True,
                    reduce_noise=True
                )
            
            # Resize back to original size if needed
            if result_image.size != original_size:
                result_image = result_image.resize(original_size, Image.LANCZOS)
            
            # Convert to requested format and encode
            output_buffer = io.BytesIO()
            save_format = self._get_save_format(request.output_format)
            result_image.save(output_buffer, format=save_format, quality=95, optimize=True)
            
            # Encode to base64
            encoded_image = base64.b64encode(output_buffer.getvalue()).decode('utf-8')
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Calculate confidence score (placeholder - would be model-specific)
            confidence = self._calculate_confidence(image, result_image)
            
            # Calculate quality metrics
            quality_metrics = self._calculate_quality_metrics(image, result_image)
            
            logger.info(f"Successfully processed request {request_id} in {processing_time:.2f}s")
            
            return WatermarkRemovalResponse(
                request_id=request_id,
                status=ProcessingStatus.COMPLETED,
                processed_image=encoded_image,
                original_size=original_size,
                processed_size=result_image.size,
                processing_time=processing_time,
                model_used=request.model_type,
                confidence=confidence,
                quality_metrics=quality_metrics,
                created_at=datetime.utcnow(),
                completed_at=datetime.utcnow()
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Failed to process request {request_id}: {e}")
            
            return WatermarkRemovalResponse(
                request_id=request_id,
                status=ProcessingStatus.FAILED,
                original_size=(0, 0),
                processed_size=(0, 0),
                processing_time=processing_time,
                model_used=request.model_type,
                confidence=0.0,
                created_at=datetime.utcnow(),
                error_message=str(e)
            )
    
    async def analyze_watermark(self, image_data: bytes) -> Dict[str, Any]:
        """Analyze image to detect watermark characteristics."""
        try:
            image = Image.open(io.BytesIO(image_data))
            
            # Detect watermark regions using image processing
            mask = self.image_processor.detect_watermark_regions(image)
            
            # Calculate detection confidence
            watermark_pixels = np.sum(mask > 0)
            total_pixels = mask.size
            confidence = min(watermark_pixels / total_pixels * 10, 1.0)  # Scale to 0-1
            
            # Determine watermark characteristics
            characteristics = self._analyze_watermark_characteristics(image, mask)
            
            # Recommend model based on analysis
            recommended_model = self._recommend_model(image, characteristics)
            
            return {
                "detected": confidence > 0.1,
                "confidence": confidence,
                "regions": self._extract_regions(mask),
                "characteristics": characteristics,
                "recommended_model": recommended_model
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze watermark: {e}")
            return {
                "detected": False,
                "confidence": 0.0,
                "regions": [],
                "characteristics": {},
                "recommended_model": "unet"
            }
    
    async def get_model_info(self) -> Dict[str, Any]:
        """Get information about available models."""
        return self.model_manager.get_model_info()
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image before model inference."""
        # Smart resize to optimal size
        processed = self.image_processor.smart_resize(image, max_size=2048)
        
        # Apply basic enhancements
        processed = self.image_processor.enhance_contrast(processed, factor=1.1)
        
        return processed
    
    def _get_save_format(self, output_format: str) -> str:
        """Get PIL save format from output format string."""
        format_map = {
            'png': 'PNG',
            'jpg': 'JPEG',
            'jpeg': 'JPEG',
            'webp': 'WebP'
        }
        return format_map.get(output_format.lower(), 'PNG')
    
    def _calculate_confidence(self, original: Image.Image, processed: Image.Image) -> float:
        """Calculate confidence score for watermark removal."""
        try:
            # Convert to numpy arrays
            orig_array = np.array(original.resize((256, 256)))
            proc_array = np.array(processed.resize((256, 256)))
            
            # Calculate structural similarity (simplified)
            mse = np.mean((orig_array - proc_array) ** 2)
            max_pixel = 255.0
            psnr = 20 * np.log10(max_pixel / np.sqrt(mse)) if mse > 0 else 100
            
            # Convert PSNR to confidence score (0-1)
            confidence = min(psnr / 40.0, 1.0)  # Normalize to 0-1
            
            return max(0.0, confidence)
            
        except Exception:
            return 0.5  # Default confidence
    
    def _calculate_quality_metrics(self, original: Image.Image, processed: Image.Image) -> Dict[str, float]:
        """Calculate quality metrics for the processed image."""
        try:
            # Resize for consistent comparison
            orig_resized = original.resize((256, 256))
            proc_resized = processed.resize((256, 256))
            
            orig_array = np.array(orig_resized).astype(np.float32)
            proc_array = np.array(proc_resized).astype(np.float32)
            
            # Calculate PSNR
            mse = np.mean((orig_array - proc_array) ** 2)
            psnr = 20 * np.log10(255.0 / np.sqrt(mse)) if mse > 0 else 100
            
            # Calculate basic sharpness metric
            gray_proc = np.mean(proc_array, axis=2) if len(proc_array.shape) == 3 else proc_array
            sharpness = np.var(np.gradient(gray_proc))
            
            # Calculate contrast
            contrast = np.std(gray_proc)
            
            # Calculate brightness
            brightness = np.mean(gray_proc)
            
            return {
                "psnr": float(psnr),
                "sharpness": float(sharpness / 1000),  # Normalize
                "contrast": float(contrast / 255),  # Normalize to 0-1
                "brightness": float(brightness / 255)  # Normalize to 0-1
            }
            
        except Exception as e:
            logger.warning(f"Failed to calculate quality metrics: {e}")
            return {}
    
    def _analyze_watermark_characteristics(self, image: Image.Image, mask: np.ndarray) -> Dict[str, Any]:
        """Analyze watermark characteristics from the image and mask."""
        try:
            characteristics = {}
            
            # Calculate watermark coverage
            coverage = np.sum(mask > 0) / mask.size
            characteristics["coverage"] = float(coverage)
            
            # Determine watermark type (text, logo, pattern)
            # This is a simplified heuristic
            if coverage < 0.05:
                characteristics["type"] = "logo"
            elif coverage < 0.2:
                characteristics["type"] = "text"
            else:
                characteristics["type"] = "pattern"
            
            # Estimate complexity
            edges = np.sum(np.gradient(mask.astype(np.float32))[0] != 0)
            complexity = min(edges / (mask.shape[0] * mask.shape[1]), 1.0)
            characteristics["complexity"] = float(complexity)
            
            # Determine position (simplified)
            y_indices, x_indices = np.where(mask > 0)
            if len(y_indices) > 0:
                center_y = np.mean(y_indices) / mask.shape[0]
                center_x = np.mean(x_indices) / mask.shape[1]
                
                if center_y < 0.3 and center_x > 0.7:
                    characteristics["position"] = "top_right"
                elif center_y > 0.7 and center_x > 0.7:
                    characteristics["position"] = "bottom_right"
                elif center_y > 0.7 and center_x < 0.3:
                    characteristics["position"] = "bottom_left"
                elif center_y < 0.3 and center_x < 0.3:
                    characteristics["position"] = "top_left"
                else:
                    characteristics["position"] = "center"
            
            return characteristics
            
        except Exception as e:
            logger.warning(f"Failed to analyze watermark characteristics: {e}")
            return {}
    
    def _recommend_model(self, image: Image.Image, characteristics: Dict[str, Any]) -> str:
        """Recommend the best model based on image and watermark characteristics."""
        try:
            # Get image size
            width, height = image.size
            total_pixels = width * height
            
            # Get watermark characteristics
            coverage = characteristics.get("coverage", 0)
            complexity = characteristics.get("complexity", 0)
            watermark_type = characteristics.get("type", "unknown")
            
            # Decision logic
            if total_pixels > 1024 * 1024:  # Large image
                return "diffusion"
            elif complexity > 0.5 or watermark_type == "pattern":  # Complex watermark
                return "diffusion"
            elif coverage > 0.3:  # Large watermark coverage
                return "diffusion"
            else:
                return "unet"  # Default to faster model
                
        except Exception:
            return "unet"  # Safe default
    
    def _extract_regions(self, mask: np.ndarray) -> list:
        """Extract watermark regions from mask."""
        try:
            # Find connected components (simplified)
            regions = []
            
            # Find bounding boxes of non-zero regions
            y_indices, x_indices = np.where(mask > 0)
            
            if len(y_indices) > 0:
                min_y, max_y = np.min(y_indices), np.max(y_indices)
                min_x, max_x = np.min(x_indices), np.max(x_indices)
                
                regions.append({
                    "bbox": [int(min_x), int(min_y), int(max_x), int(max_y)],
                    "confidence": float(np.mean(mask[mask > 0]) / 255),
                    "area": int(np.sum(mask > 0))
                })
            
            return regions
            
        except Exception:
            return []
