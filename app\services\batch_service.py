"""
Batch processing service for handling multiple images.
"""
import asyncio
import zipfile
import io
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import uuid
from loguru import logger

from app.schemas.batch import (
    BatchProcessingRequest,
    BatchProcessingResponse,
    BatchStatus,
    BatchResults,
    BatchJobSummary
)
from app.schemas.watermark import WatermarkRemovalRequest
from app.services.watermark_service import WatermarkService


class BatchService:
    """Service for batch processing of watermark removal."""
    
    def __init__(self, watermark_service: WatermarkService):
        self.watermark_service = watermark_service
        self.jobs: Dict[str, Dict[str, Any]] = {}
        self.job_results: Dict[str, Dict[str, Any]] = {}
        self.max_concurrent_jobs = 3
        self.active_jobs = set()
        
    async def process_batch(
        self,
        job_id: str,
        image_data_list: List[Dict[str, Any]],
        request: BatchProcessingRequest
    ):
        """Process a batch of images."""
        try:
            logger.info(f"Starting batch job {job_id} with {len(image_data_list)} images")
            
            # Initialize job status
            self.jobs[job_id] = {
                "status": BatchStatus.PROCESSING,
                "total_images": len(image_data_list),
                "processed_images": 0,
                "successful_images": 0,
                "failed_images": 0,
                "started_at": datetime.utcnow(),
                "request": request,
                "results": [],
                "errors": []
            }
            
            self.active_jobs.add(job_id)
            
            # Process images
            for i, image_data in enumerate(image_data_list):
                try:
                    # Create watermark removal request
                    watermark_request = WatermarkRemovalRequest(
                        model_type=request.model_type,
                        enhance_quality=request.enhance_quality,
                        output_format=request.output_format
                    )
                    
                    # Process single image
                    result = await self.watermark_service.remove_watermark(
                        image_data['data'], 
                        watermark_request
                    )
                    
                    # Store result
                    result_dict = result.dict()
                    result_dict['original_filename'] = image_data.get('filename', f'image_{i+1}')
                    self.jobs[job_id]["results"].append(result_dict)
                    
                    if result.status == "completed":
                        self.jobs[job_id]["successful_images"] += 1
                    else:
                        self.jobs[job_id]["failed_images"] += 1
                        self.jobs[job_id]["errors"].append({
                            "image_index": i,
                            "filename": image_data.get('filename'),
                            "error": result.error_message
                        })
                    
                    self.jobs[job_id]["processed_images"] += 1
                    
                    logger.info(f"Processed image {i+1}/{len(image_data_list)} for job {job_id}")
                    
                except Exception as e:
                    logger.error(f"Failed to process image {i+1} in job {job_id}: {e}")
                    self.jobs[job_id]["failed_images"] += 1
                    self.jobs[job_id]["processed_images"] += 1
                    self.jobs[job_id]["errors"].append({
                        "image_index": i,
                        "filename": image_data.get('filename'),
                        "error": str(e)
                    })
            
            # Mark job as completed
            self.jobs[job_id]["status"] = BatchStatus.COMPLETED
            self.jobs[job_id]["completed_at"] = datetime.utcnow()
            
            # Calculate processing time
            start_time = self.jobs[job_id]["started_at"]
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            self.jobs[job_id]["processing_time"] = processing_time
            
            # Store final results
            self.job_results[job_id] = self._prepare_final_results(job_id)
            
            self.active_jobs.discard(job_id)
            
            logger.info(f"Completed batch job {job_id} in {processing_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Batch job {job_id} failed: {e}")
            self.jobs[job_id]["status"] = BatchStatus.FAILED
            self.jobs[job_id]["error_message"] = str(e)
            self.active_jobs.discard(job_id)
    
    async def get_job_status(self, job_id: str) -> Optional[BatchProcessingResponse]:
        """Get the current status of a batch job."""
        if job_id not in self.jobs:
            return None
        
        job = self.jobs[job_id]
        
        # Calculate progress
        progress = 0.0
        if job["total_images"] > 0:
            progress = (job["processed_images"] / job["total_images"]) * 100
        
        # Estimate completion time
        estimated_completion = None
        if job["status"] == BatchStatus.PROCESSING and job["processed_images"] > 0:
            elapsed = (datetime.utcnow() - job["started_at"]).total_seconds()
            avg_time_per_image = elapsed / job["processed_images"]
            remaining_images = job["total_images"] - job["processed_images"]
            estimated_seconds = remaining_images * avg_time_per_image
            estimated_completion = datetime.utcnow() + timedelta(seconds=estimated_seconds)
        
        return BatchProcessingResponse(
            job_id=job_id,
            status=job["status"],
            total_images=job["total_images"],
            processed_images=job["processed_images"],
            successful_images=job["successful_images"],
            failed_images=job["failed_images"],
            progress_percentage=progress,
            created_at=job.get("created_at", job["started_at"]),
            started_at=job.get("started_at"),
            completed_at=job.get("completed_at"),
            estimated_completion=estimated_completion,
            processing_time=job.get("processing_time"),
            average_time_per_image=job.get("processing_time", 0) / max(job["processed_images"], 1),
            error_message=job.get("error_message")
        )
    
    async def get_job_results(self, job_id: str) -> Optional[BatchResults]:
        """Get the results of a completed batch job."""
        if job_id not in self.job_results:
            return None
        
        return BatchResults(**self.job_results[job_id])
    
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a running batch job."""
        if job_id not in self.jobs:
            return False
        
        if self.jobs[job_id]["status"] in [BatchStatus.COMPLETED, BatchStatus.FAILED]:
            return False
        
        self.jobs[job_id]["status"] = BatchStatus.CANCELLED
        self.jobs[job_id]["completed_at"] = datetime.utcnow()
        self.active_jobs.discard(job_id)
        
        logger.info(f"Cancelled batch job {job_id}")
        return True
    
    async def list_jobs(
        self, 
        limit: int = 10, 
        offset: int = 0, 
        status: Optional[str] = None
    ) -> List[BatchJobSummary]:
        """List batch jobs with optional filtering."""
        jobs = []
        
        for job_id, job_data in list(self.jobs.items())[offset:offset+limit]:
            if status and job_data["status"] != status:
                continue
            
            progress = 0.0
            if job_data["total_images"] > 0:
                progress = (job_data["processed_images"] / job_data["total_images"]) * 100
            
            # Estimate completion for active jobs
            estimated_completion = None
            if job_data["status"] == BatchStatus.PROCESSING and job_data["processed_images"] > 0:
                elapsed = (datetime.utcnow() - job_data["started_at"]).total_seconds()
                avg_time = elapsed / job_data["processed_images"]
                remaining = job_data["total_images"] - job_data["processed_images"]
                estimated_completion = datetime.utcnow() + timedelta(seconds=remaining * avg_time)
            
            jobs.append(BatchJobSummary(
                job_id=job_id,
                status=job_data["status"],
                total_images=job_data["total_images"],
                processed_images=job_data["processed_images"],
                progress_percentage=progress,
                created_at=job_data.get("created_at", job_data["started_at"]),
                model_type=job_data["request"].model_type,
                estimated_completion=estimated_completion
            ))
        
        return jobs
    
    async def process_zip(
        self,
        job_id: str,
        zip_data: bytes,
        model_type: str,
        enhance_quality: bool,
        output_format: str
    ):
        """Process images from a ZIP file."""
        try:
            logger.info(f"Processing ZIP file for job {job_id}")
            
            # Extract images from ZIP
            image_data_list = []
            with zipfile.ZipFile(io.BytesIO(zip_data), 'r') as zip_file:
                for file_info in zip_file.filelist:
                    if not file_info.is_dir():
                        filename = file_info.filename
                        file_ext = filename.split('.')[-1].lower()
                        
                        # Check if it's an image file
                        if file_ext in ['jpg', 'jpeg', 'png', 'webp', 'bmp']:
                            try:
                                image_data = zip_file.read(filename)
                                image_data_list.append({
                                    'data': image_data,
                                    'filename': filename,
                                    'content_type': f'image/{file_ext}'
                                })
                            except Exception as e:
                                logger.warning(f"Failed to read {filename} from ZIP: {e}")
            
            if not image_data_list:
                raise ValueError("No valid images found in ZIP file")
            
            # Create batch request
            request = BatchProcessingRequest(
                job_id=job_id,
                model_type=model_type,
                enhance_quality=enhance_quality,
                output_format=output_format,
                image_count=len(image_data_list)
            )
            
            # Process the batch
            await self.process_batch(job_id, image_data_list, request)
            
        except Exception as e:
            logger.error(f"Failed to process ZIP for job {job_id}: {e}")
            self.jobs[job_id] = {
                "status": BatchStatus.FAILED,
                "error_message": str(e),
                "total_images": 0,
                "processed_images": 0,
                "started_at": datetime.utcnow(),
                "completed_at": datetime.utcnow()
            }
    
    async def download_results(self, job_id: str) -> Optional[bytes]:
        """Create a ZIP file with batch processing results."""
        if job_id not in self.job_results:
            return None
        
        try:
            results = self.job_results[job_id]
            
            # Create ZIP file in memory
            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                
                # Add processed images
                for i, result in enumerate(results["successful_results"]):
                    if result.get("processed_image"):
                        import base64
                        image_data = base64.b64decode(result["processed_image"])
                        
                        # Get original filename or create one
                        original_filename = result.get("original_filename", f"image_{i+1}")
                        name_parts = original_filename.rsplit('.', 1)
                        if len(name_parts) == 2:
                            base_name, _ = name_parts
                        else:
                            base_name = name_parts[0]
                        
                        output_filename = f"{base_name}_processed.{result.get('output_format', 'png')}"
                        zip_file.writestr(output_filename, image_data)
                
                # Add summary report
                summary = {
                    "job_id": job_id,
                    "processing_summary": results["summary"],
                    "total_images": len(results["successful_results"]) + len(results["failed_results"]),
                    "successful_images": len(results["successful_results"]),
                    "failed_images": len(results["failed_results"]),
                    "processing_date": datetime.utcnow().isoformat()
                }
                
                zip_file.writestr("processing_report.json", json.dumps(summary, indent=2))
            
            zip_buffer.seek(0)
            return zip_buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Failed to create results ZIP for job {job_id}: {e}")
            return None
    
    def _prepare_final_results(self, job_id: str) -> Dict[str, Any]:
        """Prepare final results for a completed job."""
        job = self.jobs[job_id]
        
        successful_results = []
        failed_results = []
        
        for result in job["results"]:
            if result.get("status") == "completed":
                successful_results.append(result)
            else:
                failed_results.append(result)
        
        # Add error information to failed results
        for error in job["errors"]:
            failed_results.append({
                "image_index": error["image_index"],
                "filename": error.get("filename"),
                "error_message": error["error"],
                "status": "failed"
            })
        
        summary = {
            "total_processing_time": job.get("processing_time", 0),
            "average_time_per_image": job.get("processing_time", 0) / max(job["total_images"], 1),
            "success_rate": job["successful_images"] / max(job["total_images"], 1) * 100,
            "model_used": job["request"].model_type,
            "quality_enhancement": job["request"].enhance_quality,
            "output_format": job["request"].output_format
        }
        
        return {
            "job_id": job_id,
            "status": job["status"],
            "total_images": job["total_images"],
            "successful_results": successful_results,
            "failed_results": failed_results,
            "summary": summary,
            "expires_at": datetime.utcnow() + timedelta(hours=24)  # Results expire in 24 hours
        }
