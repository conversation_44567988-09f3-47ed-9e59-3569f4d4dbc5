"""
Configuration settings for the watermark removal service.
"""
import os
from typing import Optional
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # API Settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "AI Watermark Removal Service"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Advanced AI-powered watermark removal service"
    
    # Server Settings
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = False
    
    # Model Settings
    MODEL_PATH: str = "models/"
    DEVICE: str = "cuda" if os.system("nvidia-smi") == 0 else "cpu"
    MAX_IMAGE_SIZE: int = 2048
    SUPPORTED_FORMATS: list = ["jpg", "jpeg", "png", "webp"]
    
    # Processing Settings
    BATCH_SIZE: int = 1
    MAX_CONCURRENT_REQUESTS: int = 10
    PROCESSING_TIMEOUT: int = 300  # seconds
    
    # Storage Settings
    UPLOAD_DIR: str = "uploads/"
    OUTPUT_DIR: str = "outputs/"
    TEMP_DIR: str = "temp/"
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    
    # Redis Settings (for task queue)
    REDIS_URL: Optional[str] = None
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()

# Create necessary directories
os.makedirs(settings.MODEL_PATH, exist_ok=True)
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(settings.OUTPUT_DIR, exist_ok=True)
os.makedirs(settings.TEMP_DIR, exist_ok=True)
os.makedirs("logs", exist_ok=True)
