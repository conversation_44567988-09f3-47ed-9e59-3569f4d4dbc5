"""
FastAPI dependencies for dependency injection.
"""
from fastapi import Depends, HTTPException
from typing import Optional

from app.services.watermark_service import WatermarkService
from app.services.batch_service import BatchService
from app.services.model_manager import ModelManager


# Global service instances
_watermark_service: Optional[WatermarkService] = None
_batch_service: Optional[BatchService] = None
_model_manager: Optional[ModelManager] = None


async def get_model_manager() -> ModelManager:
    """Get the global model manager instance."""
    global _model_manager
    
    if _model_manager is None:
        from app.core.config import settings
        _model_manager = ModelManager(device=settings.DEVICE)
        await _model_manager.initialize()
    
    if not _model_manager.is_ready():
        raise HTTPException(status_code=503, detail="Models not ready")
    
    return _model_manager


async def get_watermark_service(
    model_manager: ModelManager = Depends(get_model_manager)
) -> WatermarkService:
    """Get the watermark service instance."""
    global _watermark_service
    
    if _watermark_service is None:
        _watermark_service = WatermarkService(model_manager)
    
    return _watermark_service


async def get_batch_service(
    watermark_service: WatermarkService = Depends(get_watermark_service)
) -> BatchService:
    """Get the batch service instance."""
    global _batch_service
    
    if _batch_service is None:
        _batch_service = BatchService(watermark_service)
    
    return _batch_service
