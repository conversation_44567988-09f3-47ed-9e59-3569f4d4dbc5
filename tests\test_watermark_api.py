"""
Tests for watermark removal API endpoints.
"""
import pytest
import asyncio
from fastapi.testclient import TestClient
from PIL import Image
import io
import base64

from app.main import app


client = TestClient(app)


@pytest.fixture
def sample_image():
    """Create a sample test image."""
    # Create a simple test image
    image = Image.new('RGB', (512, 512), color='white')
    
    # Add some content to make it more realistic
    from PIL import ImageDraw
    draw = ImageDraw.Draw(image)
    draw.rectangle([100, 100, 400, 400], fill='blue')
    draw.text((200, 250), "Test Image", fill='black')
    
    # Convert to bytes
    img_buffer = io.BytesIO()
    image.save(img_buffer, format='PNG')
    img_buffer.seek(0)
    
    return img_buffer.getvalue()


class TestWatermarkAPI:
    """Test watermark removal API endpoints."""
    
    def test_health_check(self):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
    
    def test_service_info(self):
        """Test service info endpoint."""
        response = client.get("/info")
        assert response.status_code == 200
        data = response.json()
        assert "service" in data
        assert "version" in data
        assert "supported_formats" in data
    
    def test_get_available_models(self):
        """Test get available models endpoint."""
        response = client.get("/api/v1/watermark/models")
        assert response.status_code in [200, 503]  # 503 if models not loaded
        
        if response.status_code == 200:
            data = response.json()
            assert "available_models" in data
    
    @pytest.mark.asyncio
    async def test_remove_watermark_invalid_file(self):
        """Test watermark removal with invalid file."""
        # Test with non-image file
        response = client.post(
            "/api/v1/watermark/remove",
            files={"image": ("test.txt", b"not an image", "text/plain")},
            data={"model_type": "unet"}
        )
        assert response.status_code == 400
    
    @pytest.mark.asyncio
    async def test_remove_watermark_valid_image(self, sample_image):
        """Test watermark removal with valid image."""
        response = client.post(
            "/api/v1/watermark/remove",
            files={"image": ("test.png", sample_image, "image/png")},
            data={
                "model_type": "unet",
                "enhance_quality": True,
                "return_format": "png"
            }
        )
        
        # Should either succeed or fail gracefully (if models not loaded)
        assert response.status_code in [200, 503]
        
        if response.status_code == 200:
            data = response.json()
            assert "request_id" in data
            assert "status" in data
    
    @pytest.mark.asyncio
    async def test_remove_watermark_stream(self, sample_image):
        """Test watermark removal with streaming response."""
        response = client.post(
            "/api/v1/watermark/remove-stream",
            files={"image": ("test.png", sample_image, "image/png")},
            data={"model_type": "unet"}
        )
        
        # Should either return image or service unavailable
        assert response.status_code in [200, 503]
        
        if response.status_code == 200:
            assert response.headers["content-type"].startswith("image/")
    
    @pytest.mark.asyncio
    async def test_analyze_watermark(self, sample_image):
        """Test watermark analysis endpoint."""
        response = client.post(
            "/api/v1/watermark/analyze",
            files={"image": ("test.png", sample_image, "image/png")}
        )
        
        assert response.status_code in [200, 503]
        
        if response.status_code == 200:
            data = response.json()
            assert "watermark_detected" in data
            assert "confidence" in data
            assert "recommended_model" in data
    
    def test_invalid_model_type(self, sample_image):
        """Test with invalid model type."""
        response = client.post(
            "/api/v1/watermark/remove",
            files={"image": ("test.png", sample_image, "image/png")},
            data={"model_type": "invalid_model"}
        )
        assert response.status_code == 422  # Validation error
    
    def test_file_too_large(self):
        """Test with file that's too large."""
        # Create a large dummy file
        large_data = b"x" * (60 * 1024 * 1024)  # 60MB
        
        response = client.post(
            "/api/v1/watermark/remove",
            files={"image": ("large.png", large_data, "image/png")},
            data={"model_type": "unet"}
        )
        assert response.status_code == 413  # File too large


class TestBatchAPI:
    """Test batch processing API endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_batch_job_no_images(self):
        """Test creating batch job with no images."""
        response = client.post(
            "/api/v1/batch/process",
            files=[],  # No files
            data={"model_type": "unet"}
        )
        assert response.status_code == 422  # Validation error
    
    @pytest.mark.asyncio
    async def test_create_batch_job_valid(self, sample_image):
        """Test creating batch job with valid images."""
        files = [
            ("images", ("test1.png", sample_image, "image/png")),
            ("images", ("test2.png", sample_image, "image/png"))
        ]
        
        response = client.post(
            "/api/v1/batch/process",
            files=files,
            data={"model_type": "unet", "enhance_quality": True}
        )
        
        assert response.status_code in [200, 503]
        
        if response.status_code == 200:
            data = response.json()
            assert "job_id" in data
            assert "status" in data
            assert data["total_images"] == 2
    
    def test_get_batch_status_not_found(self):
        """Test getting status of non-existent job."""
        response = client.get("/api/v1/batch/status/nonexistent-job-id")
        assert response.status_code == 404
    
    def test_list_batch_jobs(self):
        """Test listing batch jobs."""
        response = client.get("/api/v1/batch/jobs")
        assert response.status_code == 200
        
        data = response.json()
        assert "jobs" in data
        assert "total" in data


@pytest.mark.asyncio
async def test_concurrent_requests(sample_image):
    """Test handling multiple concurrent requests."""
    async def make_request():
        response = client.post(
            "/api/v1/watermark/remove",
            files={"image": ("test.png", sample_image, "image/png")},
            data={"model_type": "unet"}
        )
        return response.status_code
    
    # Make multiple concurrent requests
    tasks = [make_request() for _ in range(5)]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # All requests should either succeed or fail gracefully
    for result in results:
        if isinstance(result, int):
            assert result in [200, 503, 429]  # Success, unavailable, or rate limited


class TestErrorHandling:
    """Test error handling scenarios."""
    
    def test_malformed_image(self):
        """Test with malformed image data."""
        response = client.post(
            "/api/v1/watermark/remove",
            files={"image": ("test.png", b"malformed image data", "image/png")},
            data={"model_type": "unet"}
        )
        assert response.status_code in [400, 500]
    
    def test_unsupported_format(self):
        """Test with unsupported image format."""
        response = client.post(
            "/api/v1/watermark/remove",
            files={"image": ("test.gif", b"GIF89a", "image/gif")},
            data={"model_type": "unet"}
        )
        assert response.status_code == 400
    
    def test_missing_required_fields(self):
        """Test with missing required fields."""
        response = client.post("/api/v1/watermark/remove")
        assert response.status_code == 422  # Validation error


if __name__ == "__main__":
    pytest.main([__file__])
