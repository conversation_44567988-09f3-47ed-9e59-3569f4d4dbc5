#!/usr/bin/env python3
"""
Script to download pre-trained models for watermark removal.
"""
import os
import requests
import hashlib
from pathlib import Path
from tqdm import tqdm
import argparse


# Model configurations
MODELS = {
    "unet_watermark_remover": {
        "url": "https://github.com/example/watermark-models/releases/download/v1.0/unet_watermark_remover.pth",
        "filename": "unet_watermark_remover.pth",
        "size": "45MB",
        "sha256": "placeholder_hash_for_unet_model",
        "description": "U-Net based watermark removal model"
    },
    "diffusion_watermark_remover": {
        "url": "https://github.com/example/watermark-models/releases/download/v1.0/diffusion_watermark_remover.pth",
        "filename": "diffusion_watermark_remover.pth", 
        "size": "120MB",
        "sha256": "placeholder_hash_for_diffusion_model",
        "description": "Diffusion based watermark removal model"
    }
}


def download_file(url: str, filepath: Path, expected_size: str = None):
    """Download a file with progress bar."""
    print(f"Downloading {filepath.name}...")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(filepath, 'wb') as file, tqdm(
            desc=filepath.name,
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    file.write(chunk)
                    pbar.update(len(chunk))
        
        print(f"✅ Downloaded {filepath.name}")
        return True
        
    except requests.RequestException as e:
        print(f"❌ Failed to download {filepath.name}: {e}")
        return False


def verify_checksum(filepath: Path, expected_hash: str):
    """Verify file checksum."""
    if expected_hash == "placeholder_hash_for_unet_model" or expected_hash == "placeholder_hash_for_diffusion_model":
        print(f"⚠️  Skipping checksum verification for {filepath.name} (placeholder hash)")
        return True
    
    print(f"Verifying checksum for {filepath.name}...")
    
    sha256_hash = hashlib.sha256()
    with open(filepath, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            sha256_hash.update(chunk)
    
    actual_hash = sha256_hash.hexdigest()
    
    if actual_hash == expected_hash:
        print(f"✅ Checksum verified for {filepath.name}")
        return True
    else:
        print(f"❌ Checksum mismatch for {filepath.name}")
        print(f"Expected: {expected_hash}")
        print(f"Actual:   {actual_hash}")
        return False


def create_dummy_model(filepath: Path, model_name: str):
    """Create a dummy model file for testing purposes."""
    print(f"Creating dummy model: {filepath.name}")
    
    import torch
    import torch.nn as nn
    
    # Create a simple dummy model
    if "unet" in model_name:
        from app.models.unet_model import AdvancedUNet
        model = AdvancedUNet()
    else:
        from app.models.diffusion_model import DiffusionUNet
        model = DiffusionUNet()
    
    # Save model state dict
    torch.save({
        'model_state_dict': model.state_dict(),
        'model_type': model_name,
        'version': '1.0.0',
        'description': f'Dummy {model_name} model for testing'
    }, filepath)
    
    print(f"✅ Created dummy model: {filepath.name}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Download pre-trained watermark removal models")
    parser.add_argument("--model", choices=list(MODELS.keys()) + ["all"], default="all",
                       help="Specific model to download")
    parser.add_argument("--models-dir", default="models", help="Directory to save models")
    parser.add_argument("--dummy", action="store_true", help="Create dummy models for testing")
    parser.add_argument("--force", action="store_true", help="Force re-download existing models")
    
    args = parser.parse_args()
    
    # Create models directory
    models_dir = Path(args.models_dir)
    models_dir.mkdir(exist_ok=True)
    
    print(f"🚀 Model Download Script")
    print(f"Models directory: {models_dir.absolute()}")
    
    # Determine which models to process
    if args.model == "all":
        models_to_process = MODELS.keys()
    else:
        models_to_process = [args.model]
    
    success_count = 0
    total_count = len(models_to_process)
    
    for model_name in models_to_process:
        model_config = MODELS[model_name]
        filepath = models_dir / model_config["filename"]
        
        print(f"\n{'='*50}")
        print(f"Processing: {model_name}")
        print(f"Description: {model_config['description']}")
        print(f"Size: {model_config['size']}")
        print(f"{'='*50}")
        
        # Check if file already exists
        if filepath.exists() and not args.force:
            print(f"⚠️  {filepath.name} already exists. Use --force to re-download.")
            success_count += 1
            continue
        
        if args.dummy:
            # Create dummy model
            try:
                create_dummy_model(filepath, model_name)
                success_count += 1
            except Exception as e:
                print(f"❌ Failed to create dummy model {model_name}: {e}")
        else:
            # Download real model
            if download_file(model_config["url"], filepath, model_config["size"]):
                if verify_checksum(filepath, model_config["sha256"]):
                    success_count += 1
                else:
                    # Remove corrupted file
                    filepath.unlink()
    
    # Summary
    print(f"\n{'='*60}")
    print(f"DOWNLOAD SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Successful: {success_count}/{total_count}")
    print(f"❌ Failed: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 All models processed successfully!")
        
        # List downloaded models
        print(f"\nModels in {models_dir}:")
        for model_file in models_dir.glob("*.pth"):
            size = model_file.stat().st_size / (1024 * 1024)  # MB
            print(f"  📦 {model_file.name} ({size:.1f} MB)")
        
        return 0
    else:
        print("💥 Some models failed to process!")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
