"""
Batch processing API endpoints.
"""
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form, BackgroundTasks
from typing import List, Optional
import uuid
import asyncio
from datetime import datetime

from app.core.config import settings
from app.services.batch_service import BatchService
from app.schemas.batch import (
    BatchProcessingRequest,
    BatchProcessingResponse,
    BatchStatus,
    BatchJobInfo
)
from app.api.dependencies import get_batch_service

router = APIRouter()


@router.post("/process", response_model=BatchProcessingResponse)
async def create_batch_job(
    background_tasks: BackgroundTasks,
    images: List[UploadFile] = File(..., description="Multiple image files"),
    model_type: str = Form("unet", description="Model type for all images"),
    enhance_quality: bool = Form(True, description="Apply quality enhancement"),
    output_format: str = Form("png", description="Output format for all images"),
    service: BatchService = Depends(get_batch_service)
):
    """
    Create a batch processing job for multiple images.
    
    - **images**: List of image files to process
    - **model_type**: AI model to use for all images
    - **enhance_quality**: Whether to apply post-processing enhancement
    - **output_format**: Output format for all processed images
    """
    # Validate files
    if len(images) == 0:
        raise HTTPException(status_code=400, detail="No images provided")
    
    if len(images) > 50:  # Limit batch size
        raise HTTPException(status_code=400, detail="Maximum 50 images per batch")
    
    total_size = sum(image.size for image in images)
    if total_size > settings.MAX_FILE_SIZE * 10:  # Allow larger total for batch
        raise HTTPException(status_code=413, detail="Total file size too large")
    
    # Validate each file
    for i, image in enumerate(images):
        if not image.content_type or not image.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400, 
                detail=f"File {i+1} must be an image"
            )
        
        file_ext = image.filename.split('.')[-1].lower() if image.filename else ''
        if file_ext not in settings.SUPPORTED_FORMATS:
            raise HTTPException(
                status_code=400,
                detail=f"File {i+1} has unsupported format"
            )
    
    try:
        # Create batch job
        job_id = str(uuid.uuid4())
        
        # Read all image data
        image_data_list = []
        for image in images:
            data = await image.read()
            image_data_list.append({
                'data': data,
                'filename': image.filename,
                'content_type': image.content_type
            })
        
        # Create request
        request = BatchProcessingRequest(
            job_id=job_id,
            model_type=model_type,
            enhance_quality=enhance_quality,
            output_format=output_format,
            image_count=len(images)
        )
        
        # Start background processing
        background_tasks.add_task(
            service.process_batch,
            job_id,
            image_data_list,
            request
        )
        
        return BatchProcessingResponse(
            job_id=job_id,
            status=BatchStatus.QUEUED,
            total_images=len(images),
            processed_images=0,
            created_at=datetime.utcnow(),
            estimated_completion=None
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create batch job: {str(e)}")


@router.get("/status/{job_id}", response_model=BatchProcessingResponse)
async def get_batch_status(
    job_id: str,
    service: BatchService = Depends(get_batch_service)
):
    """
    Get the status of a batch processing job.
    """
    try:
        status = await service.get_job_status(job_id)
        if not status:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job status: {str(e)}")


@router.get("/results/{job_id}")
async def get_batch_results(
    job_id: str,
    service: BatchService = Depends(get_batch_service)
):
    """
    Get the results of a completed batch processing job.
    """
    try:
        results = await service.get_job_results(job_id)
        if not results:
            raise HTTPException(status_code=404, detail="Job not found or not completed")
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job results: {str(e)}")


@router.delete("/cancel/{job_id}")
async def cancel_batch_job(
    job_id: str,
    service: BatchService = Depends(get_batch_service)
):
    """
    Cancel a running batch processing job.
    """
    try:
        success = await service.cancel_job(job_id)
        if not success:
            raise HTTPException(status_code=404, detail="Job not found or cannot be cancelled")
        
        return {"message": "Job cancelled successfully", "job_id": job_id}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to cancel job: {str(e)}")


@router.get("/jobs")
async def list_batch_jobs(
    limit: int = 10,
    offset: int = 0,
    status: Optional[str] = None,
    service: BatchService = Depends(get_batch_service)
):
    """
    List batch processing jobs with optional filtering.
    """
    try:
        jobs = await service.list_jobs(limit=limit, offset=offset, status=status)
        return {
            "jobs": jobs,
            "total": len(jobs),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list jobs: {str(e)}")


@router.post("/process-zip")
async def process_zip_file(
    background_tasks: BackgroundTasks,
    zip_file: UploadFile = File(..., description="ZIP file containing images"),
    model_type: str = Form("unet"),
    enhance_quality: bool = Form(True),
    output_format: str = Form("png"),
    service: BatchService = Depends(get_batch_service)
):
    """
    Process images from a ZIP file.
    """
    if not zip_file.filename.lower().endswith('.zip'):
        raise HTTPException(status_code=400, detail="File must be a ZIP archive")
    
    if zip_file.size > settings.MAX_FILE_SIZE * 20:  # Allow larger ZIP files
        raise HTTPException(status_code=413, detail="ZIP file too large")
    
    try:
        # Create batch job for ZIP processing
        job_id = str(uuid.uuid4())
        zip_data = await zip_file.read()
        
        # Start background processing
        background_tasks.add_task(
            service.process_zip,
            job_id,
            zip_data,
            model_type,
            enhance_quality,
            output_format
        )
        
        return {
            "job_id": job_id,
            "status": "queued",
            "message": "ZIP file processing started",
            "check_status_url": f"/api/v1/batch/status/{job_id}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to process ZIP file: {str(e)}")


@router.get("/download/{job_id}")
async def download_batch_results(
    job_id: str,
    service: BatchService = Depends(get_batch_service)
):
    """
    Download batch processing results as a ZIP file.
    """
    try:
        zip_data = await service.download_results(job_id)
        if not zip_data:
            raise HTTPException(status_code=404, detail="Results not found or not ready")
        
        from fastapi.responses import StreamingResponse
        import io
        
        return StreamingResponse(
            io.BytesIO(zip_data),
            media_type="application/zip",
            headers={
                "Content-Disposition": f"attachment; filename=batch_results_{job_id}.zip"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to download results: {str(e)}")
