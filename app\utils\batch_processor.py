"""
Batch processing utilities for handling multiple images efficiently.
"""
import asyncio
import concurrent.futures
from typing import List, Dict, Any, Callable, Optional
import time
from loguru import logger
from PIL import Image
import io


class BatchProcessor:
    """Efficient batch processing for multiple images."""
    
    def __init__(self, max_workers: int = 4, max_concurrent: int = 2):
        self.max_workers = max_workers
        self.max_concurrent = max_concurrent
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
    async def process_batch(
        self,
        items: List[Any],
        processor_func: Callable,
        progress_callback: Optional[Callable] = None
    ) -> List[Dict[str, Any]]:
        """Process a batch of items with concurrency control."""
        results = []
        total_items = len(items)
        
        async def process_single_item(index: int, item: Any) -> Dict[str, Any]:
            async with self.semaphore:
                try:
                    start_time = time.time()
                    
                    # Run processor in thread pool to avoid blocking
                    loop = asyncio.get_event_loop()
                    result = await loop.run_in_executor(
                        self.executor, 
                        processor_func, 
                        item
                    )
                    
                    processing_time = time.time() - start_time
                    
                    # Call progress callback if provided
                    if progress_callback:
                        await progress_callback(index + 1, total_items, True, None)
                    
                    return {
                        "index": index,
                        "success": True,
                        "result": result,
                        "processing_time": processing_time,
                        "error": None
                    }
                    
                except Exception as e:
                    logger.error(f"Failed to process item {index}: {e}")
                    
                    # Call progress callback for error
                    if progress_callback:
                        await progress_callback(index + 1, total_items, False, str(e))
                    
                    return {
                        "index": index,
                        "success": False,
                        "result": None,
                        "processing_time": 0,
                        "error": str(e)
                    }
        
        # Create tasks for all items
        tasks = [
            process_single_item(i, item) 
            for i, item in enumerate(items)
        ]
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions that weren't caught
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "index": i,
                    "success": False,
                    "result": None,
                    "processing_time": 0,
                    "error": str(result)
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    def cleanup(self):
        """Cleanup resources."""
        if self.executor:
            self.executor.shutdown(wait=True)


class ImageBatchProcessor(BatchProcessor):
    """Specialized batch processor for images."""
    
    def __init__(self, max_workers: int = 4, max_concurrent: int = 2):
        super().__init__(max_workers, max_concurrent)
        
    async def process_image_batch(
        self,
        image_data_list: List[bytes],
        processor_func: Callable,
        progress_callback: Optional[Callable] = None
    ) -> List[Dict[str, Any]]:
        """Process a batch of image data."""
        
        def process_single_image(image_data: bytes) -> Image.Image:
            """Process a single image."""
            image = Image.open(io.BytesIO(image_data))
            return processor_func(image)
        
        return await self.process_batch(
            image_data_list,
            process_single_image,
            progress_callback
        )
    
    def validate_images(self, image_data_list: List[bytes]) -> List[Dict[str, Any]]:
        """Validate a list of image data."""
        validation_results = []
        
        for i, image_data in enumerate(image_data_list):
            try:
                image = Image.open(io.BytesIO(image_data))
                
                # Basic validation
                width, height = image.size
                format_name = image.format
                mode = image.mode
                
                validation_results.append({
                    "index": i,
                    "valid": True,
                    "width": width,
                    "height": height,
                    "format": format_name,
                    "mode": mode,
                    "size_bytes": len(image_data),
                    "error": None
                })
                
            except Exception as e:
                validation_results.append({
                    "index": i,
                    "valid": False,
                    "width": 0,
                    "height": 0,
                    "format": None,
                    "mode": None,
                    "size_bytes": len(image_data),
                    "error": str(e)
                })
        
        return validation_results
    
    def get_batch_statistics(self, validation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get statistics for a batch of images."""
        valid_images = [r for r in validation_results if r["valid"]]
        invalid_images = [r for r in validation_results if not r["valid"]]
        
        if valid_images:
            total_pixels = sum(r["width"] * r["height"] for r in valid_images)
            total_size = sum(r["size_bytes"] for r in valid_images)
            avg_width = sum(r["width"] for r in valid_images) / len(valid_images)
            avg_height = sum(r["height"] for r in valid_images) / len(valid_images)
            
            formats = {}
            for r in valid_images:
                fmt = r["format"]
                formats[fmt] = formats.get(fmt, 0) + 1
        else:
            total_pixels = 0
            total_size = 0
            avg_width = 0
            avg_height = 0
            formats = {}
        
        return {
            "total_images": len(validation_results),
            "valid_images": len(valid_images),
            "invalid_images": len(invalid_images),
            "total_pixels": total_pixels,
            "total_size_bytes": total_size,
            "total_size_mb": total_size / (1024 * 1024),
            "average_width": avg_width,
            "average_height": avg_height,
            "formats": formats,
            "success_rate": len(valid_images) / len(validation_results) if validation_results else 0
        }


class ProgressTracker:
    """Track progress of batch processing operations."""
    
    def __init__(self, total_items: int):
        self.total_items = total_items
        self.completed_items = 0
        self.successful_items = 0
        self.failed_items = 0
        self.start_time = time.time()
        self.callbacks = []
    
    def add_callback(self, callback: Callable):
        """Add a progress callback."""
        self.callbacks.append(callback)
    
    async def update_progress(self, completed: int, total: int, success: bool, error: str = None):
        """Update progress and notify callbacks."""
        self.completed_items = completed
        
        if success:
            self.successful_items += 1
        else:
            self.failed_items += 1
        
        # Calculate metrics
        elapsed_time = time.time() - self.start_time
        progress_percentage = (completed / total) * 100 if total > 0 else 0
        
        if completed > 0:
            avg_time_per_item = elapsed_time / completed
            estimated_remaining = (total - completed) * avg_time_per_item
        else:
            avg_time_per_item = 0
            estimated_remaining = 0
        
        progress_info = {
            "completed": completed,
            "total": total,
            "successful": self.successful_items,
            "failed": self.failed_items,
            "progress_percentage": progress_percentage,
            "elapsed_time": elapsed_time,
            "avg_time_per_item": avg_time_per_item,
            "estimated_remaining": estimated_remaining,
            "last_error": error
        }
        
        # Notify all callbacks
        for callback in self.callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(progress_info)
                else:
                    callback(progress_info)
            except Exception as e:
                logger.error(f"Progress callback error: {e}")
    
    def get_summary(self) -> Dict[str, Any]:
        """Get final summary of the batch processing."""
        total_time = time.time() - self.start_time
        
        return {
            "total_items": self.total_items,
            "completed_items": self.completed_items,
            "successful_items": self.successful_items,
            "failed_items": self.failed_items,
            "success_rate": self.successful_items / self.total_items if self.total_items > 0 else 0,
            "total_processing_time": total_time,
            "average_time_per_item": total_time / self.completed_items if self.completed_items > 0 else 0
        }
