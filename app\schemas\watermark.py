"""
Pydantic schemas for watermark removal API.
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class ModelType(str, Enum):
    """Available AI model types."""
    UNET = "unet"
    DIFFUSION = "diffusion"


class OutputFormat(str, Enum):
    """Supported output formats."""
    PNG = "png"
    JPG = "jpg"
    JPEG = "jpeg"
    WEBP = "webp"


class ProcessingStatus(str, Enum):
    """Processing status values."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class WatermarkRemovalRequest(BaseModel):
    """Request schema for watermark removal."""
    model_type: ModelType = Field(default=ModelType.UNET, description="AI model to use")
    enhance_quality: bool = Field(default=True, description="Apply quality enhancement")
    output_format: OutputFormat = Field(default=OutputFormat.PNG, description="Output image format")
    max_size: Optional[int] = Field(default=None, description="Maximum output size (pixels)")
    
    class Config:
        use_enum_values = True


class WatermarkRemovalResponse(BaseModel):
    """Response schema for watermark removal."""
    request_id: str = Field(description="Unique request identifier")
    status: ProcessingStatus = Field(description="Processing status")
    processed_image: Optional[str] = Field(default=None, description="Base64 encoded processed image")
    original_size: tuple[int, int] = Field(description="Original image dimensions")
    processed_size: tuple[int, int] = Field(description="Processed image dimensions")
    processing_time: float = Field(description="Processing time in seconds")
    model_used: str = Field(description="AI model used for processing")
    confidence: float = Field(description="Confidence score (0-1)")
    quality_metrics: Optional[Dict[str, float]] = Field(default=None, description="Quality metrics")
    created_at: datetime = Field(description="Request creation timestamp")
    completed_at: Optional[datetime] = Field(default=None, description="Completion timestamp")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")


class WatermarkAnalysis(BaseModel):
    """Schema for watermark analysis results."""
    detected: bool = Field(description="Whether watermark was detected")
    confidence: float = Field(description="Detection confidence (0-1)")
    regions: List[Dict[str, Any]] = Field(description="Detected watermark regions")
    characteristics: Dict[str, Any] = Field(description="Watermark characteristics")
    recommended_model: ModelType = Field(description="Recommended model for removal")
    complexity_score: float = Field(description="Watermark complexity score (0-1)")


class ModelInfo(BaseModel):
    """Schema for AI model information."""
    name: str = Field(description="Model name")
    type: ModelType = Field(description="Model type")
    description: str = Field(description="Model description")
    version: str = Field(description="Model version")
    loaded: bool = Field(description="Whether model is loaded")
    device: str = Field(description="Device model is running on")
    memory_usage: Optional[float] = Field(default=None, description="Memory usage in MB")
    avg_processing_time: Optional[float] = Field(default=None, description="Average processing time")
    supported_formats: List[str] = Field(description="Supported input formats")
    max_resolution: tuple[int, int] = Field(description="Maximum supported resolution")


class QualityMetrics(BaseModel):
    """Schema for image quality metrics."""
    psnr: Optional[float] = Field(default=None, description="Peak Signal-to-Noise Ratio")
    ssim: Optional[float] = Field(default=None, description="Structural Similarity Index")
    lpips: Optional[float] = Field(default=None, description="Learned Perceptual Image Patch Similarity")
    fid: Optional[float] = Field(default=None, description="Fréchet Inception Distance")
    sharpness: Optional[float] = Field(default=None, description="Image sharpness score")
    contrast: Optional[float] = Field(default=None, description="Image contrast score")
    brightness: Optional[float] = Field(default=None, description="Image brightness score")


class ProcessingOptions(BaseModel):
    """Advanced processing options."""
    denoise_strength: float = Field(default=0.5, ge=0.0, le=1.0, description="Denoising strength")
    enhance_contrast: bool = Field(default=True, description="Enhance contrast")
    enhance_sharpness: bool = Field(default=True, description="Enhance sharpness")
    color_correction: bool = Field(default=True, description="Apply color correction")
    super_resolution: bool = Field(default=False, description="Apply super resolution")
    preserve_details: bool = Field(default=True, description="Preserve fine details")
    
    
class ComparisonRequest(BaseModel):
    """Request schema for model comparison."""
    models: List[ModelType] = Field(description="Models to compare")
    enhance_quality: bool = Field(default=True, description="Apply quality enhancement")
    output_format: OutputFormat = Field(default=OutputFormat.PNG, description="Output format")
    include_metrics: bool = Field(default=True, description="Include quality metrics")


class ComparisonResponse(BaseModel):
    """Response schema for model comparison."""
    comparison_id: str = Field(description="Unique comparison identifier")
    timestamp: datetime = Field(description="Comparison timestamp")
    results: Dict[str, WatermarkRemovalResponse] = Field(description="Results for each model")
    best_model: Optional[str] = Field(default=None, description="Best performing model")
    metrics_comparison: Optional[Dict[str, QualityMetrics]] = Field(
        default=None, description="Quality metrics comparison"
    )
