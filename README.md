# AI Watermark Removal Service

一个基于最新深度学习技术的高效AI去水印服务，专门处理图片水印去除，如即梦AI生成的水印图片。

## 🚀 特性

- **🎯 高效去水印**: 基于先进的U-Net和Diffusion模型，实现高质量水印去除
- **⚡ 高性能API**: 提供RESTful API接口，支持单张和批量处理
- **🖼️ 多格式支持**: 支持JPG、PNG、WebP、BMP等多种图片格式
- **📦 批量处理**: 支持批量图片处理和ZIP文件处理
- **🚀 GPU加速**: 支持CUDA加速，大幅提升处理速度
- **🐳 容器化部署**: 完整的Docker部署方案
- **📊 质量增强**: 内置图像质量增强和后处理功能
- **🔍 智能分析**: 自动检测水印区域和特征
- **📈 监控支持**: 集成Prometheus监控和健康检查

## 🏗️ 技术架构

```
├── app/
│   ├── api/                 # FastAPI路由和端点
│   │   └── v1/             # API v1版本
│   ├── core/               # 核心配置和设置
│   ├── models/             # AI模型实现
│   │   ├── unet_model.py   # U-Net水印去除模型
│   │   └── diffusion_model.py # Diffusion模型
│   ├── services/           # 业务逻辑服务
│   ├── utils/              # 工具函数和图像处理
│   └── schemas/            # Pydantic数据模型
├── models/                 # 预训练模型文件
├── tests/                  # 完整测试套件
├── scripts/                # 部署和管理脚本
├── examples/               # 使用示例
└── docs/                   # 文档
```

## 🛠️ 快速安装

### 自动安装 (推荐)
```bash
# 克隆项目
git clone <repository-url>
cd watemark

# 运行自动安装脚本
python scripts/setup.py --dev

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

### 手动安装
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 创建必要目录
mkdir -p models uploads outputs temp logs

# 创建测试模型
python scripts/download_models.py --dummy
```

### 环境要求
- **Python**: 3.9+
- **CUDA**: 11.8+ (可选，用于GPU加速)
- **内存**: 建议8GB+
- **存储**: 建议10GB+可用空间

## 🚀 快速开始

### 1. 启动服务

```bash
# 开发模式 (自动重载)
make start-dev
# 或
python scripts/start_service.py --reload --monitor

# 生产模式
make start-prod
# 或
python scripts/start_service.py --workers 4
```

### 2. 验证服务
```bash
# 健康检查
curl http://localhost:8000/health

# 获取服务信息
curl http://localhost:8000/info
```

### 3. API使用示例

#### Python客户端
```python
import requests

# 单张图片去水印
with open('watermarked_image.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/api/v1/watermark/remove',
        files={'image': f},
        data={
            'model_type': 'unet',
            'enhance_quality': True,
            'return_format': 'png'
        }
    )

if response.status_code == 200:
    result = response.json()
    print(f"处理完成，耗时: {result['processing_time']:.2f}秒")

    # 保存结果
    import base64
    with open('result.png', 'wb') as f:
        f.write(base64.b64decode(result['processed_image']))
```

#### 流式API (直接返回图片)
```python
response = requests.post(
    'http://localhost:8000/api/v1/watermark/remove-stream',
    files={'image': open('watermarked_image.jpg', 'rb')},
    data={'model_type': 'diffusion'}
)

with open('result.png', 'wb') as f:
    f.write(response.content)
```

#### 批量处理
```python
# 创建批量任务
files = [
    ('images', open('image1.jpg', 'rb')),
    ('images', open('image2.jpg', 'rb')),
    ('images', open('image3.jpg', 'rb'))
]

response = requests.post(
    'http://localhost:8000/api/v1/batch/process',
    files=files,
    data={'model_type': 'unet', 'enhance_quality': True}
)

job_id = response.json()['job_id']

# 检查处理状态
status_response = requests.get(f'http://localhost:8000/api/v1/batch/status/{job_id}')
print(status_response.json())
```

### 4. 使用完整客户端示例
```bash
# 运行完整示例
python examples/client_example.py
```

## 📖 API文档

启动服务后访问以下地址查看完整API文档：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/api/v1/openapi.json

### 主要API端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/api/v1/watermark/remove` | POST | 单张图片去水印 |
| `/api/v1/watermark/remove-stream` | POST | 流式去水印 |
| `/api/v1/watermark/analyze` | POST | 水印分析 |
| `/api/v1/watermark/compare` | POST | 模型对比 |
| `/api/v1/batch/process` | POST | 批量处理 |
| `/api/v1/batch/status/{job_id}` | GET | 批量任务状态 |
| `/health` | GET | 健康检查 |
| `/info` | GET | 服务信息 |

## 🐳 Docker部署

### 快速部署
```bash
# 构建镜像
make docker-build

# 运行服务
make docker-run

# 或使用docker-compose
docker-compose up -d
```

### 生产环境部署
```bash
# 使用nginx反向代理和监控
docker-compose --profile production up -d

# 包含监控
docker-compose --profile monitoring up -d
```

### GPU支持
```bash
# 确保安装了nvidia-docker
docker run --gpus all -p 8000:8000 watermark-removal:latest
```

## 🧪 测试

```bash
# 运行所有测试
make test

# 快速测试
make test-fast

# 覆盖率测试
make test-coverage

# 集成测试
make test-integration
```

## 🔧 开发

### 代码格式化
```bash
make format  # 格式化代码
make lint    # 代码检查
```

### 添加新模型
1. 在 `app/models/` 中实现新模型
2. 继承 `WatermarkRemover` 基类
3. 在 `ModelManager` 中注册
4. 添加相应测试

### 性能优化
- 使用GPU加速: 设置 `DEVICE=cuda`
- 调整并发数: 修改 `MAX_CONCURRENT_REQUESTS`
- 模型量化: 启用 `ENABLE_MODEL_QUANTIZATION`

## 📊 监控

### Prometheus指标
- 处理请求数量
- 处理时间分布
- 错误率统计
- GPU使用率

### 健康检查
```bash
# 基础健康检查
curl http://localhost:8000/health

# 详细状态
curl http://localhost:8000/info
```

## 🔒 安全

- 文件大小限制: 50MB
- 请求频率限制: 10req/s
- 支持的格式验证
- 输入数据清理

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件
