# AI Watermark Removal Service

一个基于最新深度学习技术的高效AI去水印服务，专门处理图片水印去除，如即梦AI生成的水印图片。

## 🚀 特性

- **高效去水印**: 基于先进的深度学习模型，实现高质量水印去除
- **API服务**: 提供RESTful API接口，易于集成
- **多格式支持**: 支持JPG、PNG、WebP等多种图片格式
- **批量处理**: 支持批量图片处理
- **GPU加速**: 支持CUDA加速，提升处理速度
- **容器化部署**: 支持Docker部署

## 🏗️ 技术架构

```
├── app/
│   ├── api/                 # API路由
│   ├── core/               # 核心配置
│   ├── models/             # AI模型
│   ├── services/           # 业务逻辑
│   └── utils/              # 工具函数
├── models/                 # 预训练模型
├── tests/                  # 测试文件
├── docker/                 # Docker配置
└── docs/                   # 文档
```

## 🛠️ 安装

### 环境要求
- Python 3.9+
- CUDA 11.8+ (可选，用于GPU加速)

### 安装依赖
```bash
pip install -r requirements.txt
```

## 🚀 快速开始

### 启动服务
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### API使用示例
```python
import requests

# 上传图片去水印
with open('watermarked_image.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/api/v1/remove-watermark',
        files={'image': f}
    )
    
# 保存处理后的图片
with open('result.jpg', 'wb') as f:
    f.write(response.content)
```

## 📖 API文档

启动服务后访问 `http://localhost:8000/docs` 查看完整API文档。

## 🐳 Docker部署

```bash
docker build -t watermark-removal .
docker run -p 8000:8000 watermark-removal
```

## 📄 许可证

MIT License
