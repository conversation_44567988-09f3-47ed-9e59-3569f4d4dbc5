# 🐍 Conda 安装指南

使用conda环境是深度学习项目的最佳实践，特别适合Windows用户。

## 🚀 快速安装

### 前置要求
确保已安装 Anaconda 或 Miniconda：
- **Anaconda**: https://www.anaconda.com/products/distribution
- **Miniconda** (推荐): https://docs.conda.io/en/latest/miniconda.html

### 方法1: 一键安装 (Windows)
```cmd
# 运行conda安装脚本
scripts\setup_conda.bat
```

### 方法2: 使用Python脚本
```cmd
# 跨平台conda安装
python scripts/setup_conda.py

# 使用mamba加速 (如果已安装)
python scripts/setup_conda.py --use-mamba
```

### 方法3: 手动安装
```cmd
# 1. 创建conda环境
conda env create -f environment.yml

# 2. 激活环境
conda activate watermark-removal

# 3. 安装额外包
pip install transformers diffusers albumentations

# 4. 创建目录
mkdir models uploads outputs temp logs static

# 5. 复制配置文件
copy .env.example .env

# 6. 创建测试模型
python scripts\download_models.py --dummy
```

## 🎯 Conda的优势

### 1. 依赖管理
- ✅ 自动解决包冲突
- ✅ 二进制包，安装更快
- ✅ 跨平台兼容性好

### 2. 深度学习优化
- ✅ 预编译的PyTorch + CUDA
- ✅ 优化的数学库 (MKL, BLAS)
- ✅ GPU支持开箱即用

### 3. 环境隔离
- ✅ 完全独立的Python环境
- ✅ 不影响系统Python
- ✅ 易于备份和迁移

## 🔧 环境配置

### CUDA支持
```yaml
# environment.yml 中已包含
dependencies:
  - pytorch-cuda=11.8  # CUDA 11.8支持
  - pytorch>=2.0.0     # 最新PyTorch
```

### CPU-only版本
如果没有GPU，修改 `environment.yml`:
```yaml
dependencies:
  - pytorch-cpu  # 替换 pytorch-cuda=11.8
```

或设置环境变量：
```cmd
# 在 .env 文件中
DEVICE=cpu
```

## 🚀 启动服务

### 激活环境
```cmd
# 方法1: 直接激活
conda activate watermark-removal

# 方法2: 使用快捷脚本 (Windows)
activate_env.bat

# 方法3: 使用快捷脚本 (Linux/Mac)
./activate_env.sh
```

### 启动开发服务器
```cmd
# 确保环境已激活
conda activate watermark-removal

# 启动服务
python scripts/start_service.py --reload

# 或直接使用uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 🧪 验证安装

```cmd
# 1. 激活环境
conda activate watermark-removal

# 2. 检查Python包
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')"

# 3. 启动服务
python scripts/start_service.py

# 4. 测试API (新窗口)
curl http://localhost:8000/health

# 5. 查看API文档
# 浏览器访问: http://localhost:8000/docs
```

## 🛠️ 常用命令

### 环境管理
```cmd
# 查看所有环境
conda env list

# 激活环境
conda activate watermark-removal

# 退出环境
conda deactivate

# 删除环境 (如需重建)
conda env remove -n watermark-removal

# 导出环境
conda env export > environment_backup.yml

# 更新环境
conda env update -f environment.yml
```

### 包管理
```cmd
# 在环境中安装包
conda install -n watermark-removal package_name

# 使用pip安装 (在激活的环境中)
pip install package_name

# 列出已安装包
conda list

# 搜索包
conda search package_name
```

## 🔧 故障排除

### 问题1: 环境创建失败
```cmd
# 清理conda缓存
conda clean --all

# 使用更详细的输出
conda env create -f environment.yml --verbose

# 尝试使用mamba (更快的包解析器)
conda install mamba -c conda-forge
mamba env create -f environment.yml
```

### 问题2: CUDA不可用
```cmd
# 检查CUDA版本
nvidia-smi

# 重新安装PyTorch with CUDA
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# 或使用CPU版本
conda install pytorch torchvision torchaudio cpuonly -c pytorch
```

### 问题3: 包冲突
```cmd
# 创建最小环境
conda create -n watermark-removal python=3.9
conda activate watermark-removal

# 逐步安装关键包
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
conda install fastapi uvicorn -c conda-forge
pip install -r requirements.txt
```

### 问题4: 权限问题
```cmd
# 使用用户目录安装
conda config --add envs_dirs ~/conda/envs

# 或使用管理员权限运行命令提示符
```

## 🚀 性能优化

### 使用Mamba
```cmd
# 安装mamba (更快的包管理器)
conda install mamba -c conda-forge

# 使用mamba创建环境
mamba env create -f environment.yml

# 日常使用mamba代替conda
mamba install package_name
```

### 配置conda
```cmd
# 设置更快的镜像源 (中国用户)
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge

# 设置并行下载
conda config --set channel_priority flexible
```

## 🎯 下一步

安装完成后：

1. **测试功能**: `python examples/client_example.py`
2. **查看文档**: http://localhost:8000/docs
3. **运行测试**: `pytest tests/`
4. **部署生产**: 查看 `docker-compose.yml`

## 📚 相关资源

- [Conda用户指南](https://docs.conda.io/projects/conda/en/latest/user-guide/)
- [PyTorch安装指南](https://pytorch.org/get-started/locally/)
- [CUDA兼容性](https://docs.nvidia.com/cuda/cuda-toolkit-release-notes/)

现在您可以享受conda带来的便利了！🎉
