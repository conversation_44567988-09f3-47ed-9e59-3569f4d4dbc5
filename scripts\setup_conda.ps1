# PowerShell script for setting up AI Watermark Removal Service with Conda
Write-Host "🚀 AI Watermark Removal Service - Conda Setup (PowerShell)" -ForegroundColor Green
Write-Host "========================================================" -ForegroundColor Green

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Function to run command and check result
function Invoke-SafeCommand {
    param(
        [string]$Command,
        [string]$Description,
        [bool]$StopOnError = $true
    )
    
    Write-Host "🔧 $Description..." -ForegroundColor Yellow
    
    try {
        $result = Invoke-Expression $Command
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description completed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description failed with exit code $LASTEXITCODE" -ForegroundColor Red
            if ($StopOnError) {
                exit 1
            }
            return $false
        }
    }
    catch {
        Write-Host "❌ $Description failed: $($_.Exception.Message)" -ForegroundColor Red
        if ($StopOnError) {
            exit 1
        }
        return $false
    }
}

# Check if conda is available
Write-Host "🐍 Checking conda installation..." -ForegroundColor Cyan

if (-not (Test-Command "conda")) {
    Write-Host "❌ Conda not found. Please install Anaconda or Miniconda first." -ForegroundColor Red
    Write-Host "   Download from: https://docs.conda.io/en/latest/miniconda.html" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Conda found" -ForegroundColor Green
conda --version

# Check if mamba is available (faster package resolver)
$condaCmd = "conda"
if (Test-Command "mamba") {
    Write-Host "✅ Mamba found (faster package resolution)" -ForegroundColor Green
    $condaCmd = "mamba"
} else {
    Write-Host "⚠️  Mamba not found, using conda (slower but reliable)" -ForegroundColor Yellow
}

# Check if environment already exists
Write-Host ""
Write-Host "🏗️  Checking conda environment..." -ForegroundColor Cyan

$envExists = $false
try {
    $envList = conda env list
    if ($envList -match "watermark-removal") {
        Write-Host "✅ Environment 'watermark-removal' already exists" -ForegroundColor Green
        $envExists = $true
    }
}
catch {
    Write-Host "⚠️  Could not check existing environments" -ForegroundColor Yellow
}

# Create conda environment if it doesn't exist
if (-not $envExists) {
    Write-Host "Creating conda environment from environment.yml..." -ForegroundColor Cyan
    
    if (Test-Path "environment.yml") {
        $success = Invoke-SafeCommand "$condaCmd env create -f environment.yml" "Creating conda environment"
        if (-not $success) {
            Write-Host "Trying with conda instead of mamba..." -ForegroundColor Yellow
            Invoke-SafeCommand "conda env create -f environment.yml" "Creating conda environment with conda"
        }
    } else {
        Write-Host "❌ environment.yml not found" -ForegroundColor Red
        exit 1
    }
}

# Install additional packages
Write-Host ""
Write-Host "📦 Installing additional packages..." -ForegroundColor Cyan
Invoke-SafeCommand "conda run -n watermark-removal pip install transformers diffusers albumentations" "Installing additional packages" $false

# Create directories
Write-Host ""
Write-Host "📁 Creating directories..." -ForegroundColor Cyan

$directories = @("models", "uploads", "outputs", "temp", "logs", "static")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ Created $dir/" -ForegroundColor Green
    } else {
        Write-Host "✅ Directory $dir/ already exists" -ForegroundColor Green
    }
}

# Setup environment file
Write-Host ""
Write-Host "📝 Setting up environment file..." -ForegroundColor Cyan

if (-not (Test-Path ".env")) {
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✅ .env file created from .env.example" -ForegroundColor Green
        Write-Host "⚠️  Please review and update .env file with your settings" -ForegroundColor Yellow
    } else {
        Write-Host "⚠️  .env.example not found" -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ .env file already exists" -ForegroundColor Green
}

# Create activation script
Write-Host ""
Write-Host "📝 Creating activation scripts..." -ForegroundColor Cyan

# PowerShell activation script
$psScript = @"
Write-Host "Activating watermark-removal conda environment..." -ForegroundColor Green
conda activate watermark-removal
Write-Host "✅ Environment activated!" -ForegroundColor Green
Write-Host ""
Write-Host "Available commands:" -ForegroundColor Cyan
Write-Host "  python scripts\start_service.py --reload  # Start development server" -ForegroundColor White
Write-Host "  python scripts\download_models.py --dummy # Create test models" -ForegroundColor White
Write-Host "  python examples\client_example.py         # Run example client" -ForegroundColor White
Write-Host ""
"@

$psScript | Out-File -FilePath "activate_env.ps1" -Encoding UTF8
Write-Host "✅ Created activate_env.ps1" -ForegroundColor Green

# Batch activation script for compatibility
$batScript = @"
@echo off
echo Activating watermark-removal conda environment...
call conda activate watermark-removal
echo ✅ Environment activated!
echo.
echo Available commands:
echo   python scripts\start_service.py --reload  # Start development server
echo   python scripts\download_models.py --dummy # Create test models
echo   python examples\client_example.py         # Run example client
echo.
cmd /k
"@

$batScript | Out-File -FilePath "activate_env.bat" -Encoding ASCII
Write-Host "✅ Created activate_env.bat" -ForegroundColor Green

# Create test models
Write-Host ""
Write-Host "🤖 Creating test models..." -ForegroundColor Cyan
Invoke-SafeCommand "conda run -n watermark-removal python scripts\download_models.py --dummy" "Creating test models" $false

# Test installation
Write-Host ""
Write-Host "🧪 Testing installation..." -ForegroundColor Cyan

$testScript = @"
import torch
import fastapi
import PIL
import cv2
print('✅ All imports successful')
print(f'PyTorch: {torch.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA device: {torch.cuda.get_device_name(0)}')
"@

$testScript | Out-File -FilePath "test_imports.py" -Encoding UTF8

try {
    $testResult = conda run -n watermark-removal python test_imports.py
    Write-Host $testResult -ForegroundColor Green
    Remove-Item "test_imports.py" -Force
}
catch {
    Write-Host "❌ Installation test failed" -ForegroundColor Red
    Remove-Item "test_imports.py" -Force -ErrorAction SilentlyContinue
}

# Success message
Write-Host ""
Write-Host "========================================================" -ForegroundColor Green
Write-Host "🎉 Conda setup completed successfully!" -ForegroundColor Green
Write-Host "========================================================" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Activate environment:" -ForegroundColor White
Write-Host "   conda activate watermark-removal" -ForegroundColor Yellow
Write-Host "   Or run: .\activate_env.ps1" -ForegroundColor Yellow
Write-Host ""
Write-Host "2. Review configuration:" -ForegroundColor White
Write-Host "   notepad .env" -ForegroundColor Yellow
Write-Host ""
Write-Host "3. Start the service:" -ForegroundColor White
Write-Host "   python scripts\start_service.py --reload" -ForegroundColor Yellow
Write-Host ""
Write-Host "4. Access the service:" -ForegroundColor White
Write-Host "   - API Documentation: http://localhost:8000/docs" -ForegroundColor Yellow
Write-Host "   - Health Check: http://localhost:8000/health" -ForegroundColor Yellow
Write-Host ""
Write-Host "5. Run tests:" -ForegroundColor White
Write-Host "   pytest tests/" -ForegroundColor Yellow
Write-Host ""
Write-Host "For quick start, run: .\activate_env.ps1" -ForegroundColor Green
Write-Host ""

Read-Host "Press Enter to continue"
