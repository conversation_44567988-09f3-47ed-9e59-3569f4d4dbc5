"""
Base watermark remover class and advanced implementations.
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from abc import ABC, abstractmethod
from typing import Union, Tuple
import numpy as np
from PIL import Image
import cv2


class WatermarkRemover(ABC):
    """Abstract base class for watermark removal models."""
    
    def __init__(self, device: str = "cuda"):
        self.device = torch.device(device if torch.cuda.is_available() else "cpu")
        self.model = None
        
    @abstractmethod
    def load_model(self, model_path: str = None):
        """Load the watermark removal model."""
        pass
    
    @abstractmethod
    def remove_watermark(self, image: Union[np.ndarray, Image.Image]) -> Image.Image:
        """Remove watermark from image."""
        pass
    
    def preprocess_image(self, image: Union[np.ndarray, Image.Image], 
                        target_size: Tuple[int, int] = (512, 512)) -> torch.Tensor:
        """Preprocess image for model input."""
        if isinstance(image, Image.Image):
            image = np.array(image)
        
        # Convert BGR to RGB if needed
        if len(image.shape) == 3 and image.shape[2] == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Resize image
        image = cv2.resize(image, target_size)
        
        # Normalize to [0, 1]
        image = image.astype(np.float32) / 255.0
        
        # Convert to tensor and add batch dimension
        tensor = torch.from_numpy(image).permute(2, 0, 1).unsqueeze(0)
        return tensor.to(self.device)
    
    def postprocess_output(self, output: torch.Tensor) -> Image.Image:
        """Convert model output to PIL Image."""
        # Remove batch dimension and convert to numpy
        output = output.squeeze(0).cpu().detach().numpy()
        
        # Transpose from CHW to HWC
        output = np.transpose(output, (1, 2, 0))
        
        # Clip values to [0, 1] and convert to [0, 255]
        output = np.clip(output, 0, 1) * 255
        output = output.astype(np.uint8)
        
        return Image.fromarray(output)


class ResidualBlock(nn.Module):
    """Residual block for U-Net architecture."""
    
    def __init__(self, in_channels: int, out_channels: int):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, 3, padding=1)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.conv2 = nn.Conv2d(out_channels, out_channels, 3, padding=1)
        self.bn2 = nn.BatchNorm2d(out_channels)
        
        self.shortcut = nn.Sequential()
        if in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1),
                nn.BatchNorm2d(out_channels)
            )
    
    def forward(self, x):
        residual = self.shortcut(x)
        out = F.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += residual
        return F.relu(out)


class AttentionBlock(nn.Module):
    """Self-attention block for enhanced feature extraction."""
    
    def __init__(self, channels: int):
        super().__init__()
        self.channels = channels
        self.query = nn.Conv2d(channels, channels // 8, 1)
        self.key = nn.Conv2d(channels, channels // 8, 1)
        self.value = nn.Conv2d(channels, channels, 1)
        self.gamma = nn.Parameter(torch.zeros(1))
        
    def forward(self, x):
        batch_size, channels, height, width = x.size()
        
        # Generate query, key, value
        q = self.query(x).view(batch_size, -1, height * width).permute(0, 2, 1)
        k = self.key(x).view(batch_size, -1, height * width)
        v = self.value(x).view(batch_size, -1, height * width)
        
        # Compute attention
        attention = torch.bmm(q, k)
        attention = F.softmax(attention, dim=-1)
        
        # Apply attention to values
        out = torch.bmm(v, attention.permute(0, 2, 1))
        out = out.view(batch_size, channels, height, width)
        
        # Apply gamma and residual connection
        out = self.gamma * out + x
        return out
