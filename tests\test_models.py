"""
Tests for AI models.
"""
import pytest
import torch
import numpy as np
from PIL import Image

from app.models.unet_model import UNet<PERSON><PERSON>markRemover, AdvancedUNet
from app.models.diffusion_model import DiffusionWatermarkRemover
from app.utils.image_processor import ImageProcessor


class TestUNetModel:
    """Test U-Net watermark removal model."""
    
    @pytest.fixture
    def model(self):
        """Create U-Net model instance."""
        return UNetWatermarkRemover(device="cpu")
    
    @pytest.fixture
    def sample_image(self):
        """Create sample image for testing."""
        return Image.new('RGB', (256, 256), color='white')
    
    def test_model_initialization(self, model):
        """Test model initialization."""
        assert model.device.type == "cpu"
        assert model.model is not None
        assert isinstance(model.model, AdvancedUNet)
    
    def test_model_forward_pass(self, model):
        """Test model forward pass."""
        # Create dummy input
        dummy_input = torch.randn(1, 3, 256, 256)
        
        with torch.no_grad():
            output = model.model(dummy_input)
        
        assert output.shape == dummy_input.shape
        assert torch.all(output >= 0) and torch.all(output <= 1)  # Sigmoid output
    
    def test_preprocess_image(self, model, sample_image):
        """Test image preprocessing."""
        tensor = model.preprocess_image(sample_image, target_size=(256, 256))
        
        assert isinstance(tensor, torch.Tensor)
        assert tensor.shape == (1, 3, 256, 256)
        assert tensor.device == model.device
    
    def test_postprocess_output(self, model):
        """Test output postprocessing."""
        # Create dummy output tensor
        output_tensor = torch.rand(1, 3, 256, 256)
        
        result_image = model.postprocess_output(output_tensor)
        
        assert isinstance(result_image, Image.Image)
        assert result_image.size == (256, 256)
    
    def test_remove_watermark(self, model, sample_image):
        """Test complete watermark removal pipeline."""
        # Load model (will use random weights)
        model.load_model()
        
        result = model.remove_watermark(sample_image)
        
        assert isinstance(result, Image.Image)
        assert result.size == sample_image.size


class TestDiffusionModel:
    """Test Diffusion watermark removal model."""
    
    @pytest.fixture
    def model(self):
        """Create Diffusion model instance."""
        return DiffusionWatermarkRemover(device="cpu", num_timesteps=100)  # Fewer timesteps for testing
    
    @pytest.fixture
    def sample_image(self):
        """Create sample image for testing."""
        return Image.new('RGB', (256, 256), color='white')
    
    def test_model_initialization(self, model):
        """Test model initialization."""
        assert model.device.type == "cpu"
        assert model.num_timesteps == 100
        assert model.model is not None
    
    def test_noise_schedule(self, model):
        """Test noise schedule initialization."""
        assert len(model.betas) == model.num_timesteps
        assert len(model.alphas) == model.num_timesteps
        assert len(model.alphas_cumprod) == model.num_timesteps
        
        # Check that betas are in valid range
        assert torch.all(model.betas > 0) and torch.all(model.betas < 1)
    
    def test_denoise_process(self, model, sample_image):
        """Test denoising process."""
        # Load model (will use random weights)
        model.load_model()
        
        # Preprocess image
        input_tensor = model.preprocess_image(sample_image, target_size=(256, 256))
        
        # Run denoising with fewer steps for testing
        with torch.no_grad():
            result = model.denoise(input_tensor, num_steps=5)
        
        assert result.shape == input_tensor.shape
    
    def test_remove_watermark(self, model, sample_image):
        """Test complete watermark removal pipeline."""
        # Load model (will use random weights)
        model.load_model()
        
        result = model.remove_watermark(sample_image)
        
        assert isinstance(result, Image.Image)
        assert result.size == sample_image.size


class TestImageProcessor:
    """Test image processing utilities."""
    
    @pytest.fixture
    def processor(self):
        """Create image processor instance."""
        return ImageProcessor()
    
    @pytest.fixture
    def sample_image(self):
        """Create sample image for testing."""
        return Image.new('RGB', (512, 512), color='white')
    
    def test_smart_resize(self, processor, sample_image):
        """Test smart resize functionality."""
        # Test resize when image is larger than max_size
        large_image = Image.new('RGB', (3000, 2000), color='white')
        resized = processor.smart_resize(large_image, max_size=1024)
        
        assert max(resized.size) == 1024
        assert resized.size[0] / resized.size[1] == large_image.size[0] / large_image.size[1]
        
        # Test no resize when image is smaller
        small_resized = processor.smart_resize(sample_image, max_size=1024)
        assert small_resized.size == sample_image.size
    
    def test_detect_watermark_regions(self, processor, sample_image):
        """Test watermark region detection."""
        # Add some content to the image to simulate watermark
        from PIL import ImageDraw
        draw = ImageDraw.Draw(sample_image)
        draw.text((400, 400), "WATERMARK", fill='gray')
        
        mask = processor.detect_watermark_regions(sample_image)
        
        assert isinstance(mask, np.ndarray)
        assert mask.shape == (sample_image.size[1], sample_image.size[0])
        assert mask.dtype == np.uint8
    
    def test_enhance_contrast(self, processor, sample_image):
        """Test contrast enhancement."""
        enhanced = processor.enhance_contrast(sample_image, factor=1.2)
        
        assert isinstance(enhanced, Image.Image)
        assert enhanced.size == sample_image.size
    
    def test_reduce_noise(self, processor, sample_image):
        """Test noise reduction."""
        denoised = processor.reduce_noise(sample_image, method='bilateral')
        
        assert isinstance(denoised, Image.Image)
        assert denoised.size == sample_image.size
    
    def test_supported_formats(self, processor):
        """Test supported format validation."""
        assert 'jpg' in processor.supported_formats
        assert 'png' in processor.supported_formats
        assert 'webp' in processor.supported_formats


class TestModelIntegration:
    """Integration tests for models with real-world scenarios."""
    
    @pytest.fixture
    def watermarked_image(self):
        """Create a synthetic watermarked image."""
        # Create base image
        image = Image.new('RGB', (512, 512), color='lightblue')
        
        # Add watermark
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(image)
        
        # Add text watermark
        draw.text((400, 450), "© Test", fill='white')
        
        # Add semi-transparent overlay
        overlay = Image.new('RGBA', (100, 50), (255, 255, 255, 128))
        image.paste(overlay, (400, 400), overlay)
        
        return image.convert('RGB')
    
    @pytest.mark.slow
    def test_unet_on_watermarked_image(self, watermarked_image):
        """Test U-Net model on watermarked image."""
        model = UNetWatermarkRemover(device="cpu")
        model.load_model()
        
        result = model.remove_watermark(watermarked_image)
        
        assert isinstance(result, Image.Image)
        assert result.size == watermarked_image.size
        
        # Basic quality check - result should be different from input
        result_array = np.array(result)
        input_array = np.array(watermarked_image)
        
        # Images should be different (watermark removal should change pixels)
        assert not np.array_equal(result_array, input_array)
    
    @pytest.mark.slow
    def test_diffusion_on_watermarked_image(self, watermarked_image):
        """Test Diffusion model on watermarked image."""
        model = DiffusionWatermarkRemover(device="cpu", num_timesteps=50)
        model.load_model()
        
        result = model.remove_watermark(watermarked_image)
        
        assert isinstance(result, Image.Image)
        assert result.size == watermarked_image.size
    
    def test_model_comparison(self, watermarked_image):
        """Compare different models on the same image."""
        unet_model = UNetWatermarkRemover(device="cpu")
        unet_model.load_model()
        
        diffusion_model = DiffusionWatermarkRemover(device="cpu", num_timesteps=20)
        diffusion_model.load_model()
        
        unet_result = unet_model.remove_watermark(watermarked_image)
        diffusion_result = diffusion_model.remove_watermark(watermarked_image)
        
        # Both should produce valid results
        assert isinstance(unet_result, Image.Image)
        assert isinstance(diffusion_result, Image.Image)
        
        # Results should be different (different algorithms)
        unet_array = np.array(unet_result)
        diffusion_array = np.array(diffusion_result)
        assert not np.array_equal(unet_array, diffusion_array)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
