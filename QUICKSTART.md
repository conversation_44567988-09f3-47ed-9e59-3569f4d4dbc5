# 🚀 快速开始指南

本指南将帮助您在5分钟内启动并运行AI去水印服务。

## 📋 前置要求

- Python 3.9+
- Git
- 8GB+ 内存
- (可选) NVIDIA GPU + CUDA 11.8+

## 🎯 一键安装

### 1. 克隆项目
```bash
git clone <repository-url>
cd watemark
```

### 2. 自动安装
```bash
# 开发环境安装 (包含测试工具)
python scripts/setup.py --dev

# 生产环境安装
python scripts/setup.py
```

### 3. 激活环境
```bash
# Linux/Mac
source venv/bin/activate

# Windows
venv\Scripts\activate
```

### 4. 启动服务
```bash
# 开发模式 (自动重载)
make start-dev

# 或手动启动
python scripts/start_service.py --reload
```

### 5. 验证安装
```bash
# 检查服务状态
curl http://localhost:8000/health

# 查看API文档
open http://localhost:8000/docs
```

## 🎮 快速测试

### 使用内置示例
```bash
# 运行完整示例 (需要提供测试图片)
python examples/client_example.py
```

### 手动测试API
```bash
# 测试单张图片去水印
curl -X POST "http://localhost:8000/api/v1/watermark/remove" \
  -H "Content-Type: multipart/form-data" \
  -F "image=@your_image.jpg" \
  -F "model_type=unet" \
  -F "enhance_quality=true"
```

### Python代码测试
```python
import requests
import base64

# 去水印
with open('test_image.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/api/v1/watermark/remove',
        files={'image': f},
        data={'model_type': 'unet'}
    )

# 保存结果
if response.status_code == 200:
    result = response.json()
    with open('result.png', 'wb') as f:
        f.write(base64.b64decode(result['processed_image']))
    print("✅ 去水印完成!")
```

## 🐳 Docker快速部署

### 单容器部署
```bash
# 构建并运行
make docker-build
make docker-run

# 或一步完成
docker-compose up --build
```

### 完整生产环境
```bash
# 包含nginx、redis、监控
docker-compose --profile production up -d
```

## 🔧 常见问题

### Q: 服务启动失败
```bash
# 检查依赖
python scripts/setup.py --check-deps

# 查看详细错误
python scripts/start_service.py --reload
```

### Q: GPU不可用
```bash
# 检查CUDA
nvidia-smi

# 强制使用CPU
export DEVICE=cpu
```

### Q: 模型文件缺失
```bash
# 创建测试模型
python scripts/download_models.py --dummy

# 下载真实模型 (如果可用)
python scripts/download_models.py
```

### Q: 端口被占用
```bash
# 使用其他端口
python scripts/start_service.py --port 8001
```

## 📚 下一步

1. **阅读完整文档**: [README.md](README.md)
2. **查看API文档**: http://localhost:8000/docs
3. **运行测试**: `make test`
4. **性能调优**: 编辑 `.env` 文件
5. **部署生产**: 使用Docker Compose

## 🆘 获取帮助

- 查看日志: `tail -f logs/app.log`
- 运行诊断: `make health`
- 检查配置: `curl http://localhost:8000/info`

## 🎉 成功！

如果您看到以下输出，说明服务已成功运行：

```json
{
  "status": "healthy",
  "details": {
    "service": "running",
    "models": "loaded"
  }
}
```

现在您可以开始使用AI去水印服务了！
