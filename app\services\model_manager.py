"""
Model manager for loading and managing AI models.
"""
import torch
import asyncio
from typing import Dict, Optional, Any
import os
from loguru import logger

from app.core.config import settings
from app.models import UNetWatermarkRemover, DiffusionWatermarkRemover
from app.utils.image_processor import ImageProcessor


class ModelManager:
    """Manages AI models for watermark removal."""
    
    def __init__(self, device: str = "cuda"):
        self.device = torch.device(device if torch.cuda.is_available() else "cpu")
        self.models: Dict[str, Any] = {}
        self.model_info: Dict[str, Dict] = {}
        self.image_processor = ImageProcessor()
        self._ready = False
        
        logger.info(f"ModelManager initialized with device: {self.device}")
    
    async def initialize(self):
        """Initialize and load all models."""
        try:
            logger.info("Loading AI models...")
            
            # Load U-Net model
            await self._load_unet_model()
            
            # Load Diffusion model
            await self._load_diffusion_model()
            
            self._ready = True
            logger.info("All models loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize models: {e}")
            raise
    
    async def _load_unet_model(self):
        """Load U-Net watermark removal model."""
        try:
            model = UNetWatermarkRemover(device=str(self.device))
            
            # Try to load pre-trained weights
            model_path = os.path.join(settings.MODEL_PATH, "unet_watermark_remover.pth")
            if os.path.exists(model_path):
                model.load_model(model_path)
                logger.info("Loaded pre-trained U-Net model")
            else:
                model.load_model()  # Initialize with random weights
                logger.warning("U-Net model initialized with random weights")
            
            self.models["unet"] = model
            self.model_info["unet"] = {
                "name": "U-Net Watermark Remover",
                "type": "unet",
                "description": "Fast and efficient watermark removal using U-Net architecture",
                "version": "1.0.0",
                "loaded": True,
                "device": str(self.device),
                "supported_formats": settings.SUPPORTED_FORMATS,
                "max_resolution": (2048, 2048),
                "avg_processing_time": 2.5  # seconds
            }
            
        except Exception as e:
            logger.error(f"Failed to load U-Net model: {e}")
            raise
    
    async def _load_diffusion_model(self):
        """Load Diffusion watermark removal model."""
        try:
            model = DiffusionWatermarkRemover(device=str(self.device))
            
            # Try to load pre-trained weights
            model_path = os.path.join(settings.MODEL_PATH, "diffusion_watermark_remover.pth")
            if os.path.exists(model_path):
                model.load_model(model_path)
                logger.info("Loaded pre-trained Diffusion model")
            else:
                model.load_model()  # Initialize with random weights
                logger.warning("Diffusion model initialized with random weights")
            
            self.models["diffusion"] = model
            self.model_info["diffusion"] = {
                "name": "Diffusion Watermark Remover",
                "type": "diffusion",
                "description": "High-quality watermark removal using diffusion models",
                "version": "1.0.0",
                "loaded": True,
                "device": str(self.device),
                "supported_formats": settings.SUPPORTED_FORMATS,
                "max_resolution": (1024, 1024),
                "avg_processing_time": 8.0  # seconds
            }
            
        except Exception as e:
            logger.error(f"Failed to load Diffusion model: {e}")
            raise
    
    def get_model(self, model_type: str):
        """Get a specific model by type."""
        if not self._ready:
            raise RuntimeError("Models not initialized")
        
        if model_type not in self.models:
            raise ValueError(f"Model type '{model_type}' not available")
        
        return self.models[model_type]
    
    def get_model_info(self) -> Dict[str, Dict]:
        """Get information about all loaded models."""
        return self.model_info.copy()
    
    def is_ready(self) -> bool:
        """Check if all models are loaded and ready."""
        return self._ready
    
    def get_available_models(self) -> list:
        """Get list of available model types."""
        return list(self.models.keys())
    
    def get_recommended_model(self, image_size: tuple, quality_priority: bool = False) -> str:
        """Get recommended model based on image characteristics."""
        width, height = image_size
        total_pixels = width * height
        
        # For large images or when quality is priority, use diffusion
        if quality_priority or total_pixels > 1024 * 1024:
            return "diffusion"
        else:
            return "unet"
    
    async def cleanup(self):
        """Cleanup resources."""
        try:
            logger.info("Cleaning up models...")
            
            for model_name, model in self.models.items():
                if hasattr(model, 'cleanup'):
                    await model.cleanup()
                del model
                logger.info(f"Cleaned up {model_name} model")
            
            self.models.clear()
            self.model_info.clear()
            self._ready = False
            
            # Clear GPU cache if using CUDA
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                logger.info("Cleared CUDA cache")
                
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get memory usage information."""
        memory_info = {}
        
        if torch.cuda.is_available():
            memory_info["gpu_allocated"] = torch.cuda.memory_allocated() / 1024**2  # MB
            memory_info["gpu_cached"] = torch.cuda.memory_reserved() / 1024**2  # MB
            memory_info["gpu_max_allocated"] = torch.cuda.max_memory_allocated() / 1024**2  # MB
        
        # Add CPU memory info if needed
        import psutil
        process = psutil.Process()
        memory_info["cpu_memory"] = process.memory_info().rss / 1024**2  # MB
        
        return memory_info
    
    async def warm_up_models(self):
        """Warm up models with dummy data to improve first inference speed."""
        try:
            logger.info("Warming up models...")
            
            # Create dummy image tensor
            dummy_input = torch.randn(1, 3, 512, 512).to(self.device)
            
            for model_name, model in self.models.items():
                try:
                    with torch.no_grad():
                        if hasattr(model, 'model'):
                            _ = model.model(dummy_input)
                    logger.info(f"Warmed up {model_name} model")
                except Exception as e:
                    logger.warning(f"Failed to warm up {model_name}: {e}")
            
            logger.info("Model warm-up completed")
            
        except Exception as e:
            logger.error(f"Error during model warm-up: {e}")
    
    def get_model_performance_stats(self) -> Dict[str, Dict]:
        """Get performance statistics for each model."""
        stats = {}
        
        for model_name, info in self.model_info.items():
            stats[model_name] = {
                "avg_processing_time": info.get("avg_processing_time", 0),
                "max_resolution": info.get("max_resolution", (0, 0)),
                "memory_efficient": model_name == "unet",  # U-Net is more memory efficient
                "quality_score": 0.9 if model_name == "diffusion" else 0.8,  # Diffusion typically higher quality
                "speed_score": 0.9 if model_name == "unet" else 0.6  # U-Net is faster
            }
        
        return stats
