#!/usr/bin/env python3
"""
Test runner script for the watermark removal service.
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*50}")
    
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if result.stdout:
        print("STDOUT:")
        print(result.stdout)
    
    if result.stderr:
        print("STDERR:")
        print(result.stderr)
    
    if result.returncode != 0:
        print(f"❌ {description} failed with return code {result.returncode}")
        return False
    else:
        print(f"✅ {description} completed successfully")
        return True


def main():
    """Main test runner."""
    # Change to project root directory
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    print("🚀 Starting AI Watermark Removal Service Tests")
    print(f"Working directory: {os.getcwd()}")
    
    # Check if virtual environment is activated
    if not os.environ.get('VIRTUAL_ENV'):
        print("⚠️  Warning: No virtual environment detected")
    
    success_count = 0
    total_tests = 0
    
    # 1. Code formatting check
    total_tests += 1
    if run_command("black --check app/ tests/", "Code formatting check (black)"):
        success_count += 1
    
    # 2. Import sorting check
    total_tests += 1
    if run_command("isort --check-only app/ tests/", "Import sorting check (isort)"):
        success_count += 1
    
    # 3. Linting
    total_tests += 1
    if run_command("flake8 app/ tests/ --max-line-length=100 --ignore=E203,W503", "Code linting (flake8)"):
        success_count += 1
    
    # 4. Unit tests (fast)
    total_tests += 1
    if run_command("pytest tests/ -m 'not slow' -v", "Unit tests (fast)"):
        success_count += 1
    
    # 5. Integration tests (if requested)
    if "--integration" in sys.argv:
        total_tests += 1
        if run_command("pytest tests/ -m 'slow or integration' -v", "Integration tests"):
            success_count += 1
    
    # 6. Coverage report (if requested)
    if "--coverage" in sys.argv:
        total_tests += 1
        if run_command("pytest tests/ --cov=app --cov-report=html --cov-report=term", "Coverage report"):
            success_count += 1
    
    # 7. API tests (if server is running)
    if "--api" in sys.argv:
        total_tests += 1
        if run_command("pytest tests/test_watermark_api.py -v", "API tests"):
            success_count += 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Passed: {success_count}/{total_tests}")
    print(f"❌ Failed: {total_tests - success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 All tests passed!")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
