"""
Pydantic schemas for batch processing API.
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

from .watermark import ModelType, OutputFormat, WatermarkRemovalResponse


class BatchStatus(str, Enum):
    """Batch processing status values."""
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PARTIAL = "partial"  # Some images processed successfully


class BatchProcessingRequest(BaseModel):
    """Request schema for batch processing."""
    job_id: str = Field(description="Unique job identifier")
    model_type: ModelType = Field(default=ModelType.UNET, description="AI model to use")
    enhance_quality: bool = Field(default=True, description="Apply quality enhancement")
    output_format: OutputFormat = Field(default=OutputFormat.PNG, description="Output format")
    image_count: int = Field(description="Number of images to process")
    priority: int = Field(default=0, description="Job priority (higher = more priority)")
    
    class Config:
        use_enum_values = True


class BatchProcessingResponse(BaseModel):
    """Response schema for batch processing."""
    job_id: str = Field(description="Unique job identifier")
    status: BatchStatus = Field(description="Current job status")
    total_images: int = Field(description="Total number of images")
    processed_images: int = Field(description="Number of processed images")
    successful_images: int = Field(default=0, description="Number of successfully processed images")
    failed_images: int = Field(default=0, description="Number of failed images")
    progress_percentage: float = Field(description="Progress percentage (0-100)")
    created_at: datetime = Field(description="Job creation timestamp")
    started_at: Optional[datetime] = Field(default=None, description="Processing start timestamp")
    completed_at: Optional[datetime] = Field(default=None, description="Completion timestamp")
    estimated_completion: Optional[datetime] = Field(default=None, description="Estimated completion time")
    processing_time: Optional[float] = Field(default=None, description="Total processing time in seconds")
    average_time_per_image: Optional[float] = Field(default=None, description="Average time per image")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    
    @property
    def is_completed(self) -> bool:
        """Check if job is completed (successfully or with errors)."""
        return self.status in [BatchStatus.COMPLETED, BatchStatus.FAILED, BatchStatus.PARTIAL]
    
    @property
    def is_active(self) -> bool:
        """Check if job is currently active."""
        return self.status in [BatchStatus.QUEUED, BatchStatus.PROCESSING]


class BatchJobInfo(BaseModel):
    """Detailed information about a batch job."""
    job_id: str = Field(description="Unique job identifier")
    status: BatchStatus = Field(description="Current job status")
    request: BatchProcessingRequest = Field(description="Original request")
    response: BatchProcessingResponse = Field(description="Current response")
    results: Optional[List[WatermarkRemovalResponse]] = Field(
        default=None, description="Individual image results"
    )
    failed_images: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Information about failed images"
    )
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class BatchResults(BaseModel):
    """Results of a completed batch job."""
    job_id: str = Field(description="Job identifier")
    status: BatchStatus = Field(description="Final job status")
    total_images: int = Field(description="Total number of images")
    successful_results: List[WatermarkRemovalResponse] = Field(description="Successful results")
    failed_results: List[Dict[str, Any]] = Field(description="Failed results with error info")
    summary: Dict[str, Any] = Field(description="Processing summary")
    download_url: Optional[str] = Field(default=None, description="URL to download results")
    expires_at: Optional[datetime] = Field(default=None, description="Results expiration time")


class BatchStatistics(BaseModel):
    """Statistics for batch processing."""
    total_jobs: int = Field(description="Total number of jobs")
    active_jobs: int = Field(description="Number of active jobs")
    completed_jobs: int = Field(description="Number of completed jobs")
    failed_jobs: int = Field(description="Number of failed jobs")
    total_images_processed: int = Field(description="Total images processed")
    average_processing_time: float = Field(description="Average processing time per image")
    success_rate: float = Field(description="Success rate percentage")
    queue_length: int = Field(description="Current queue length")


class BatchJobSummary(BaseModel):
    """Summary information for listing jobs."""
    job_id: str = Field(description="Job identifier")
    status: BatchStatus = Field(description="Job status")
    total_images: int = Field(description="Total number of images")
    processed_images: int = Field(description="Number of processed images")
    progress_percentage: float = Field(description="Progress percentage")
    created_at: datetime = Field(description="Creation timestamp")
    model_type: str = Field(description="Model type used")
    estimated_completion: Optional[datetime] = Field(default=None, description="Estimated completion")


class ZipProcessingRequest(BaseModel):
    """Request schema for ZIP file processing."""
    job_id: str = Field(description="Unique job identifier")
    model_type: ModelType = Field(default=ModelType.UNET, description="AI model to use")
    enhance_quality: bool = Field(default=True, description="Apply quality enhancement")
    output_format: OutputFormat = Field(default=OutputFormat.PNG, description="Output format")
    extract_to_folder: bool = Field(default=False, description="Extract images to separate folders")
    preserve_structure: bool = Field(default=True, description="Preserve ZIP folder structure")
    
    class Config:
        use_enum_values = True


class BatchConfiguration(BaseModel):
    """Configuration for batch processing."""
    max_concurrent_jobs: int = Field(default=5, description="Maximum concurrent jobs")
    max_images_per_job: int = Field(default=100, description="Maximum images per job")
    job_timeout_minutes: int = Field(default=60, description="Job timeout in minutes")
    result_retention_hours: int = Field(default=24, description="Result retention time")
    auto_cleanup: bool = Field(default=True, description="Automatic cleanup of old results")
    priority_queue: bool = Field(default=True, description="Enable priority queue")
    
    
class BatchError(BaseModel):
    """Error information for batch processing."""
    image_index: int = Field(description="Index of the failed image")
    filename: Optional[str] = Field(default=None, description="Original filename")
    error_code: str = Field(description="Error code")
    error_message: str = Field(description="Detailed error message")
    timestamp: datetime = Field(description="Error timestamp")
    retry_count: int = Field(default=0, description="Number of retry attempts")
