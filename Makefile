# Makefile for AI Watermark Removal Service

.PHONY: help install dev-install test lint format clean docker-build docker-run start stop models

# Default target
help:
	@echo "AI Watermark Removal Service - Available Commands:"
	@echo ""
	@echo "Setup:"
	@echo "  install      Install production dependencies"
	@echo "  dev-install  Install development dependencies"
	@echo "  models       Download/create model files"
	@echo ""
	@echo "Development:"
	@echo "  test         Run all tests"
	@echo "  test-fast    Run fast tests only"
	@echo "  lint         Run code linting"
	@echo "  format       Format code with black and isort"
	@echo "  clean        Clean up temporary files"
	@echo ""
	@echo "Service:"
	@echo "  start        Start the service"
	@echo "  start-dev    Start service in development mode"
	@echo "  stop         Stop the service"
	@echo "  restart      Restart the service"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build Build Docker image"
	@echo "  docker-run   Run service in Docker"
	@echo "  docker-dev   Run development environment"

# Installation
install:
	pip install -r requirements.txt

dev-install: install
	pip install pytest pytest-asyncio black isort flake8 jupyter

# Model management
models:
	python scripts/download_models.py --dummy

models-real:
	python scripts/download_models.py

# Testing
test:
	python scripts/run_tests.py

test-fast:
	pytest tests/ -m "not slow" -v

test-coverage:
	python scripts/run_tests.py --coverage

test-integration:
	python scripts/run_tests.py --integration

# Code quality
lint:
	flake8 app/ tests/ --max-line-length=100 --ignore=E203,W503
	black --check app/ tests/
	isort --check-only app/ tests/

format:
	black app/ tests/
	isort app/ tests/

# Service management
start:
	python scripts/start_service.py

start-dev:
	python scripts/start_service.py --reload --monitor

start-prod:
	python scripts/start_service.py --workers 4

stop:
	pkill -f "uvicorn app.main:app" || true
	pkill -f "gunicorn app.main:app" || true

restart: stop start

# Docker
docker-build:
	docker build -t watermark-removal:latest .

docker-build-dev:
	docker build -t watermark-removal:dev --target development .

docker-run:
	docker run -p 8000:8000 --gpus all watermark-removal:latest

docker-dev:
	docker-compose up --build

docker-stop:
	docker-compose down

# Cleanup
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf dist/
	rm -rf build/
	rm -rf uploads/*
	rm -rf outputs/*
	rm -rf temp/*

# Development helpers
jupyter:
	jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root

docs:
	@echo "Starting service for API documentation..."
	@echo "Visit http://localhost:8000/docs for interactive API docs"
	@echo "Visit http://localhost:8000/redoc for alternative docs"
	python scripts/start_service.py

# Health check
health:
	curl -f http://localhost:8000/health || echo "Service not running"

# Example usage
example:
	python examples/client_example.py

# Setup development environment
setup-dev: dev-install models
	@echo "Development environment setup complete!"
	@echo "Run 'make start-dev' to start the service"

# Production deployment
deploy-check:
	@echo "Pre-deployment checks..."
	python scripts/run_tests.py
	python scripts/start_service.py --check-deps
	@echo "✅ Ready for deployment"

# Monitoring
logs:
	tail -f logs/app.log

# Database operations (if needed in future)
db-init:
	@echo "Database initialization not implemented yet"

db-migrate:
	@echo "Database migration not implemented yet"

# Performance testing
perf-test:
	@echo "Performance testing not implemented yet"
	@echo "Consider using tools like locust or ab for load testing"

# Security scan
security-scan:
	@echo "Security scanning not implemented yet"
	@echo "Consider using tools like bandit for security analysis"
